---
layout: home

hero:
  name: "Web3 开发"
  text: "中文完整指南"
  tagline: Solidity 智能合约 + Ethers.js 前端开发
  actions:
    - theme: brand
      text: Solidity 教程
      link: /solidity/basics/introduction
    - theme: alt
      text: Ethers.js 教程
      link: /ethers/basics/introduction

features:
  - title: 🔗 Solidity 智能合约
    details: 从零开始学习 Solidity，掌握智能合约开发的核心技能
  - title: 🌐 Ethers.js 前端开发
    details: 学习使用 Ethers.js 构建 Web3 前端应用，连接区块链
  - title: 🛠️ 开发工具链
    details: 掌握 Hardhat、Remix、Foundry 等主流开发工具
  - title: 🛡️ 安全最佳实践
    details: 学习智能合约和 DApp 安全开发的最佳实践
  - title: ⚡ 性能优化
    details: 掌握 Gas 优化和前端性能优化技巧
  - title: 🚀 实战项目
    details: 通过 DeFi、NFT、DAO 等实战项目巩固知识
---



