{"hash": "9f33d1cf", "configHash": "c95c5b9f", "lockfileHash": "ba44e772", "browserHash": "0f3c2bee", "optimized": {"vue": {"src": "../../../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "24349d1b", "needsInterop": false}, "vitepress > @vue/devtools-api": {"src": "../../../../../../node_modules/@vue/devtools-api/dist/index.js", "file": "vitepress___@vue_devtools-api.js", "fileHash": "9ea0d469", "needsInterop": false}, "vitepress > @vueuse/core": {"src": "../../../../../../node_modules/@vueuse/core/index.mjs", "file": "vitepress___@vueuse_core.js", "fileHash": "8748d543", "needsInterop": false}, "vitepress > @vueuse/integrations/useFocusTrap": {"src": "../../../../../../node_modules/@vueuse/integrations/useFocusTrap.mjs", "file": "vitepress___@vueuse_integrations_useFocusTrap.js", "fileHash": "d689d8e8", "needsInterop": false}, "vitepress > mark.js/src/vanilla.js": {"src": "../../../../../../node_modules/mark.js/src/vanilla.js", "file": "vitepress___mark__js_src_vanilla__js.js", "fileHash": "6ba2ad50", "needsInterop": false}, "vitepress > minisearch": {"src": "../../../../../../node_modules/minisearch/dist/es/index.js", "file": "vitepress___minisearch.js", "fileHash": "912462c6", "needsInterop": false}}, "chunks": {"chunk-MA5A6G4L": {"file": "chunk-MA5A6G4L.js"}, "chunk-GVSSDPL4": {"file": "chunk-GVSSDPL4.js"}}}