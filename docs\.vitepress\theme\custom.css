/* 搜索框样式优化 */
.VPNavBarSearch {
  flex-grow: 1;
  padding-left: 24px;
}

@media (min-width: 768px) {
  .VPNavBarSearch {
    max-width: 200px;
  }
}

/* 搜索按钮样式 */
.DocSearch-Button {
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 0 10px 0 12px;
  outline: none;
  cursor: pointer;
  margin: 0;
  width: 100%;
  height: 32px;
  color: var(--vp-c-text-2);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  transition: border-color 0.25s, background-color 0.25s;
}

.DocSearch-Button:hover {
  border-color: var(--vp-c-brand-1);
  background: var(--vp-c-bg);
}

.DocSearch-Button-Container {
  display: flex;
  align-items: center;
}

.DocSearch-Search-Icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  fill: var(--vp-c-text-3);
}

.DocSearch-Button-Placeholder {
  font-size: 13px;
  font-weight: 500;
  color: var(--vp-c-text-3);
  transition: color 0.25s;
}

.DocSearch-Button-Keys {
  display: flex;
  margin-left: auto;
  margin-right: -4px;
}

.DocSearch-Button-Key {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 4px;
  border: 1px solid var(--vp-c-divider);
  border-radius: 4px;
  background: var(--vp-c-bg-mute);
  box-shadow: inset 0 -2px 0 0 var(--vp-c-divider), inset 0 0 1px 1px var(--vp-c-bg);
  font-size: 11px;
  font-weight: 500;
  line-height: 20px;
  color: var(--vp-c-text-3);
  transition: all 0.25s;
}

/* 搜索结果样式 */
.DocSearch-Modal {
  background: var(--vp-c-bg);
  border-radius: 12px;
  box-shadow: var(--vp-shadow-3);
}

.DocSearch-SearchBar {
  background: var(--vp-c-bg);
  border-bottom: 1px solid var(--vp-c-divider);
}

.DocSearch-Form {
  background: var(--vp-c-bg);
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
}

.DocSearch-Input {
  background: transparent;
  color: var(--vp-c-text-1);
}

.DocSearch-Input::placeholder {
  color: var(--vp-c-text-3);
}

.DocSearch-Dropdown {
  background: var(--vp-c-bg);
}

.DocSearch-Hit {
  background: var(--vp-c-bg);
  border-bottom: 1px solid var(--vp-c-divider);
}

.DocSearch-Hit[aria-selected="true"] {
  background: var(--vp-c-bg-soft);
}

.DocSearch-Hit-Container {
  background: transparent;
}

.DocSearch-Hit-Tree {
  color: var(--vp-c-text-2);
}

.DocSearch-Hit-title {
  color: var(--vp-c-text-1);
}

.DocSearch-Hit-path {
  color: var(--vp-c-text-2);
}

.DocSearch-Hit-text {
  color: var(--vp-c-text-2);
}

.DocSearch-Footer {
  background: var(--vp-c-bg);
  border-top: 1px solid var(--vp-c-divider);
}

.DocSearch-Commands {
  color: var(--vp-c-text-2);
}

.DocSearch-Label {
  background: var(--vp-c-bg-mute);
  border: 1px solid var(--vp-c-border);
  color: var(--vp-c-text-2);
}

/* 移动端搜索优化 */
@media (max-width: 768px) {
  .VPNavBarSearch {
    padding-left: 0;
  }
  
  .DocSearch-Button {
    width: 32px;
    height: 32px;
    padding: 0;
    justify-content: center;
  }
  
  .DocSearch-Button-Placeholder,
  .DocSearch-Button-Keys {
    display: none;
  }
}

/* 高亮搜索结果 */
.DocSearch-Hit mark {
  background: var(--vp-c-brand-soft);
  color: var(--vp-c-brand-1);
  padding: 0 2px;
  border-radius: 2px;
}

/* 搜索无结果状态 */
.DocSearch-NoResults {
  background: var(--vp-c-bg);
  color: var(--vp-c-text-2);
}

.DocSearch-Title {
  color: var(--vp-c-text-1);
}

/* 搜索快捷键提示 */
.search-shortcut-hint {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--vp-c-text-2);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 1000;
}

.search-shortcut-hint.show {
  opacity: 1;
  transform: translateY(0);
}

/* API 标签样式 */
.api-tag {
  display: inline-block;
  background: var(--vp-c-brand-soft);
  color: var(--vp-c-brand-1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  margin-left: 8px;
  vertical-align: middle;
}

.api-tag.hook {
  background: var(--vp-c-green-soft);
  color: var(--vp-c-green-1);
}

.api-tag.function {
  background: var(--vp-c-blue-soft);
  color: var(--vp-c-blue-1);
}

.api-tag.type {
  background: var(--vp-c-purple-soft);
  color: var(--vp-c-purple-1);
}

/* 搜索结果分类 */
.search-category {
  font-size: 11px;
  font-weight: 600;
  color: var(--vp-c-text-3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 16px 0 8px 0;
  padding: 0 16px;
}

/* 代码块在搜索结果中的样式 */
.DocSearch-Hit code {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-code);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.85em;
}