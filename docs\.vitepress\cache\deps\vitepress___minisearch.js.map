{"version": 3, "sources": ["../../../../../../node_modules/minisearch/src/SearchableMap/TreeIterator.ts", "../../../../../../node_modules/minisearch/src/SearchableMap/fuzzySearch.ts", "../../../../../../node_modules/minisearch/src/SearchableMap/SearchableMap.ts", "../../../../../../node_modules/minisearch/src/MiniSearch.ts"], "sourcesContent": ["import type { RadixTree, Entry, LeafType } from './types'\n\n/** @ignore */\nconst ENTRIES = 'ENTRIES'\n\n/** @ignore */\nconst KEYS = 'KEYS'\n\n/** @ignore */\nconst VALUES = 'VALUES'\n\n/** @ignore */\nconst LEAF = '' as LeafType\n\ninterface Iterators<T> {\n  ENTRIES: Entry<T>\n  KEYS: string\n  VALUES: T\n}\n\ntype Kind<T> = keyof Iterators<T>\ntype Result<T, K extends keyof Iterators<T>> = Iterators<T>[K]\n\ntype IteratorPath<T> = {\n  node: RadixTree<T>,\n  keys: string[]\n}[]\n\nexport type IterableSet<T> = {\n  _tree: RadixTree<T>,\n  _prefix: string\n}\n\n/**\n * @private\n */\nclass TreeIterator<T, K extends Kind<T>> implements Iterator<Result<T, K>> {\n  set: IterableSet<T>\n  _type: K\n  _path: IteratorPath<T>\n\n  constructor (set: IterableSet<T>, type: K) {\n    const node = set._tree\n    const keys = Array.from(node.keys())\n    this.set = set\n    this._type = type\n    this._path = keys.length > 0 ? [{ node, keys }] : []\n  }\n\n  next (): IteratorResult<Result<T, K>> {\n    const value = this.dive()\n    this.backtrack()\n    return value\n  }\n\n  dive (): IteratorResult<Result<T, K>> {\n    if (this._path.length === 0) { return { done: true, value: undefined } }\n    const { node, keys } = last(this._path)!\n    if (last(keys) === LEAF) { return { done: false, value: this.result() } }\n\n    const child = node.get(last(keys)!)!\n    this._path.push({ node: child, keys: Array.from(child.keys()) })\n    return this.dive()\n  }\n\n  backtrack (): void {\n    if (this._path.length === 0) { return }\n    const keys = last(this._path)!.keys\n    keys.pop()\n    if (keys.length > 0) { return }\n    this._path.pop()\n    this.backtrack()\n  }\n\n  key (): string {\n    return this.set._prefix + this._path\n      .map(({ keys }) => last(keys))\n      .filter(key => key !== LEAF)\n      .join('')\n  }\n\n  value (): T {\n    return last(this._path)!.node.get(LEAF)!\n  }\n\n  result (): Result<T, K> {\n    switch (this._type) {\n      case VALUES: return this.value() as Result<T, K>\n      case KEYS: return this.key() as Result<T, K>\n      default: return [this.key(), this.value()] as Result<T, K>\n    }\n  }\n\n  [Symbol.iterator] () {\n    return this\n  }\n}\n\nconst last = <T>(array: T[]): T | undefined => {\n  return array[array.length - 1]\n}\n\nexport { TreeIterator, ENTRIES, KEYS, VALUES, LEAF }\n", "/* eslint-disable no-labels */\nimport { LEAF } from './TreeIterator'\nimport type { RadixTree } from './types'\n\nexport type FuzzyResult<T> = [T, number]\n\nexport type FuzzyResults<T> = Map<string, FuzzyResult<T>>\n\n/**\n * @ignore\n */\nexport const fuzzySearch = <T = any>(node: RadixTree<T>, query: string, maxDistance: number): FuzzyResults<T> => {\n  const results: FuzzyResults<T> = new Map()\n  if (query === undefined) return results\n\n  // Number of columns in the Levenshtein matrix.\n  const n = query.length + 1\n\n  // Matching terms can never be longer than N + maxDistance.\n  const m = n + maxDistance\n\n  // Fill first matrix row and column with numbers: 0 1 2 3 ...\n  const matrix = new Uint8Array(m * n).fill(maxDistance + 1)\n  for (let j = 0; j < n; ++j) matrix[j] = j\n  for (let i = 1; i < m; ++i) matrix[i * n] = i\n\n  recurse(\n    node,\n    query,\n    maxDistance,\n    results,\n    matrix,\n    1,\n    n,\n    ''\n  )\n\n  return results\n}\n\n// Modified version of http://stevehanov.ca/blog/?id=114\n\n// This builds a Levenshtein matrix for a given query and continuously updates\n// it for nodes in the radix tree that fall within the given maximum edit\n// distance. Keeping the same matrix around is beneficial especially for larger\n// edit distances.\n//\n//           k   a   t   e   <-- query\n//       0   1   2   3   4\n//   c   1   1   2   3   4\n//   a   2   2   1   2   3\n//   t   3   3   2   1  [2]  <-- edit distance\n//   ^\n//   ^ term in radix tree, rows are added and removed as needed\n\nconst recurse = <T = any>(\n  node: RadixTree<T>,\n  query: string,\n  maxDistance: number,\n  results: FuzzyResults<T>,\n  matrix: Uint8Array,\n  m: number,\n  n: number,\n  prefix: string\n): void => {\n  const offset = m * n\n\n  key: for (const key of node.keys()) {\n    if (key === LEAF) {\n      // We've reached a leaf node. Check if the edit distance acceptable and\n      // store the result if it is.\n      const distance = matrix[offset - 1]\n      if (distance <= maxDistance) {\n        results.set(prefix, [node.get(key)!, distance])\n      }\n    } else {\n      // Iterate over all characters in the key. Update the Levenshtein matrix\n      // and check if the minimum distance in the last row is still within the\n      // maximum edit distance. If it is, we can recurse over all child nodes.\n      let i = m\n      for (let pos = 0; pos < key.length; ++pos, ++i) {\n        const char = key[pos]\n        const thisRowOffset = n * i\n        const prevRowOffset = thisRowOffset - n\n\n        // Set the first column based on the previous row, and initialize the\n        // minimum distance in the current row.\n        let minDistance = matrix[thisRowOffset]\n\n        const jmin = Math.max(0, i - maxDistance - 1)\n        const jmax = Math.min(n - 1, i + maxDistance)\n\n        // Iterate over remaining columns (characters in the query).\n        for (let j = jmin; j < jmax; ++j) {\n          const different = char !== query[j]\n\n          // It might make sense to only read the matrix positions used for\n          // deletion/insertion if the characters are different. But we want to\n          // avoid conditional reads for performance reasons.\n          const rpl = matrix[prevRowOffset + j] + +different\n          const del = matrix[prevRowOffset + j + 1] + 1\n          const ins = matrix[thisRowOffset + j] + 1\n\n          const dist = matrix[thisRowOffset + j + 1] = Math.min(rpl, del, ins)\n\n          if (dist < minDistance) minDistance = dist\n        }\n\n        // Because distance will never decrease, we can stop. There will be no\n        // matching child nodes.\n        if (minDistance > maxDistance) {\n          continue key\n        }\n      }\n\n      recurse(\n        node.get(key)!,\n        query,\n        maxDistance,\n        results,\n        matrix,\n        i,\n        n,\n        prefix + key\n      )\n    }\n  }\n}\n\nexport default fuzzySearch\n", "/* eslint-disable no-labels */\nimport { TreeIterator, ENTRIES, KEYS, VALUES, LEAF } from './TreeIterator'\nimport fuzzySearch, { type FuzzyResults } from './fuzzySearch'\nimport type { RadixTree, Entry, Path } from './types'\n\n/**\n * A class implementing the same interface as a standard JavaScript\n * [`Map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map)\n * with string keys, but adding support for efficiently searching entries with\n * prefix or fuzzy search. This class is used internally by {@link MiniSearch}\n * as the inverted index data structure. The implementation is a radix tree\n * (compressed prefix tree).\n *\n * Since this class can be of general utility beyond _MiniSearch_, it is\n * exported by the `minisearch` package and can be imported (or required) as\n * `minisearch/SearchableMap`.\n *\n * @typeParam T  The type of the values stored in the map.\n */\nexport default class SearchableMap<T = any> {\n  /**\n   * @internal\n   */\n  _tree: RadixTree<T>\n\n  /**\n   * @internal\n   */\n  _prefix: string\n\n  private _size: number | undefined = undefined\n\n  /**\n   * The constructor is normally called without arguments, creating an empty\n   * map. In order to create a {@link SearchableMap} from an iterable or from an\n   * object, check {@link SearchableMap.from} and {@link\n   * SearchableMap.fromObject}.\n   *\n   * The constructor arguments are for internal use, when creating derived\n   * mutable views of a map at a prefix.\n   */\n  constructor (tree: RadixTree<T> = new Map(), prefix = '') {\n    this._tree = tree\n    this._prefix = prefix\n  }\n\n  /**\n   * Creates and returns a mutable view of this {@link SearchableMap},\n   * containing only entries that share the given prefix.\n   *\n   * ### Usage:\n   *\n   * ```javascript\n   * let map = new SearchableMap()\n   * map.set(\"unicorn\", 1)\n   * map.set(\"universe\", 2)\n   * map.set(\"university\", 3)\n   * map.set(\"unique\", 4)\n   * map.set(\"hello\", 5)\n   *\n   * let uni = map.atPrefix(\"uni\")\n   * uni.get(\"unique\") // => 4\n   * uni.get(\"unicorn\") // => 1\n   * uni.get(\"hello\") // => undefined\n   *\n   * let univer = map.atPrefix(\"univer\")\n   * univer.get(\"unique\") // => undefined\n   * univer.get(\"universe\") // => 2\n   * univer.get(\"university\") // => 3\n   * ```\n   *\n   * @param prefix  The prefix\n   * @return A {@link SearchableMap} representing a mutable view of the original\n   * Map at the given prefix\n   */\n  atPrefix (prefix: string): SearchableMap<T> {\n    if (!prefix.startsWith(this._prefix)) { throw new Error('Mismatched prefix') }\n\n    const [node, path] = trackDown(this._tree, prefix.slice(this._prefix.length))\n\n    if (node === undefined) {\n      const [parentNode, key] = last(path)\n\n      for (const k of parentNode!.keys()) {\n        if (k !== LEAF && k.startsWith(key)) {\n          const node = new Map()\n          node.set(k.slice(key.length), parentNode!.get(k)!)\n          return new SearchableMap(node, prefix)\n        }\n      }\n    }\n\n    return new SearchableMap<T>(node, prefix)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/clear\n   */\n  clear (): void {\n    this._size = undefined\n    this._tree.clear()\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/delete\n   * @param key  Key to delete\n   */\n  delete (key: string): void {\n    this._size = undefined\n    return remove(this._tree, key)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/entries\n   * @return An iterator iterating through `[key, value]` entries.\n   */\n  entries () {\n    return new TreeIterator(this, ENTRIES)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/forEach\n   * @param fn  Iteration function\n   */\n  forEach (fn: (key: string, value: T, map: SearchableMap) => void): void {\n    for (const [key, value] of this) {\n      fn(key, value, this)\n    }\n  }\n\n  /**\n   * Returns a Map of all the entries that have a key within the given edit\n   * distance from the search key. The keys of the returned Map are the matching\n   * keys, while the values are two-element arrays where the first element is\n   * the value associated to the key, and the second is the edit distance of the\n   * key to the search key.\n   *\n   * ### Usage:\n   *\n   * ```javascript\n   * let map = new SearchableMap()\n   * map.set('hello', 'world')\n   * map.set('hell', 'yeah')\n   * map.set('ciao', 'mondo')\n   *\n   * // Get all entries that match the key 'hallo' with a maximum edit distance of 2\n   * map.fuzzyGet('hallo', 2)\n   * // => Map(2) { 'hello' => ['world', 1], 'hell' => ['yeah', 2] }\n   *\n   * // In the example, the \"hello\" key has value \"world\" and edit distance of 1\n   * // (change \"e\" to \"a\"), the key \"hell\" has value \"yeah\" and edit distance of 2\n   * // (change \"e\" to \"a\", delete \"o\")\n   * ```\n   *\n   * @param key  The search key\n   * @param maxEditDistance  The maximum edit distance (Levenshtein)\n   * @return A Map of the matching keys to their value and edit distance\n   */\n  fuzzyGet (key: string, maxEditDistance: number): FuzzyResults<T> {\n    return fuzzySearch<T>(this._tree, key, maxEditDistance)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/get\n   * @param key  Key to get\n   * @return Value associated to the key, or `undefined` if the key is not\n   * found.\n   */\n  get (key: string): T | undefined {\n    const node = lookup<T>(this._tree, key)\n    return node !== undefined ? node.get(LEAF) : undefined\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/has\n   * @param key  Key\n   * @return True if the key is in the map, false otherwise\n   */\n  has (key: string): boolean {\n    const node = lookup(this._tree, key)\n    return node !== undefined && node.has(LEAF)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/keys\n   * @return An `Iterable` iterating through keys\n   */\n  keys () {\n    return new TreeIterator(this, KEYS)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/set\n   * @param key  Key to set\n   * @param value  Value to associate to the key\n   * @return The {@link SearchableMap} itself, to allow chaining\n   */\n  set (key: string, value: T): SearchableMap<T> {\n    if (typeof key !== 'string') { throw new Error('key must be a string') }\n    this._size = undefined\n    const node = createPath(this._tree, key)\n    node.set(LEAF, value)\n    return this\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/size\n   */\n  get size (): number {\n    if (this._size) { return this._size }\n    /** @ignore */\n    this._size = 0\n\n    const iter = this.entries()\n    while (!iter.next().done) this._size! += 1\n\n    return this._size\n  }\n\n  /**\n   * Updates the value at the given key using the provided function. The function\n   * is called with the current value at the key, and its return value is used as\n   * the new value to be set.\n   *\n   * ### Example:\n   *\n   * ```javascript\n   * // Increment the current value by one\n   * searchableMap.update('somekey', (currentValue) => currentValue == null ? 0 : currentValue + 1)\n   * ```\n   *\n   * If the value at the given key is or will be an object, it might not require\n   * re-assignment. In that case it is better to use `fetch()`, because it is\n   * faster.\n   *\n   * @param key  The key to update\n   * @param fn  The function used to compute the new value from the current one\n   * @return The {@link SearchableMap} itself, to allow chaining\n   */\n  update (key: string, fn: (value: T | undefined) => T): SearchableMap<T> {\n    if (typeof key !== 'string') { throw new Error('key must be a string') }\n    this._size = undefined\n    const node = createPath(this._tree, key)\n    node.set(LEAF, fn(node.get(LEAF)))\n    return this\n  }\n\n  /**\n   * Fetches the value of the given key. If the value does not exist, calls the\n   * given function to create a new value, which is inserted at the given key\n   * and subsequently returned.\n   *\n   * ### Example:\n   *\n   * ```javascript\n   * const map = searchableMap.fetch('somekey', () => new Map())\n   * map.set('foo', 'bar')\n   * ```\n   *\n   * @param key  The key to update\n   * @param initial  A function that creates a new value if the key does not exist\n   * @return The existing or new value at the given key\n   */\n  fetch (key: string, initial: () => T): T {\n    if (typeof key !== 'string') { throw new Error('key must be a string') }\n    this._size = undefined\n    const node = createPath(this._tree, key)\n\n    let value = node.get(LEAF)\n    if (value === undefined) {\n      node.set(LEAF, value = initial())\n    }\n\n    return value\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/values\n   * @return An `Iterable` iterating through values.\n   */\n  values () {\n    return new TreeIterator(this, VALUES)\n  }\n\n  /**\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/@@iterator\n   */\n  [Symbol.iterator] () {\n    return this.entries()\n  }\n\n  /**\n   * Creates a {@link SearchableMap} from an `Iterable` of entries\n   *\n   * @param entries  Entries to be inserted in the {@link SearchableMap}\n   * @return A new {@link SearchableMap} with the given entries\n   */\n  static from<T = any> (entries: Iterable<Entry<T>> | Entry<T>[]) {\n    const tree = new SearchableMap()\n    for (const [key, value] of entries) {\n      tree.set(key, value)\n    }\n    return tree\n  }\n\n  /**\n   * Creates a {@link SearchableMap} from the iterable properties of a JavaScript object\n   *\n   * @param object  Object of entries for the {@link SearchableMap}\n   * @return A new {@link SearchableMap} with the given entries\n   */\n  static fromObject<T = any> (object: { [key: string]: T }) {\n    return SearchableMap.from<T>(Object.entries(object))\n  }\n}\n\nconst trackDown = <T = any>(tree: RadixTree<T> | undefined, key: string, path: Path<T> = []): [RadixTree<T> | undefined, Path<T>] => {\n  if (key.length === 0 || tree == null) { return [tree, path] }\n\n  for (const k of tree.keys()) {\n    if (k !== LEAF && key.startsWith(k)) {\n      path.push([tree, k]) // performance: update in place\n      return trackDown(tree.get(k)!, key.slice(k.length), path)\n    }\n  }\n\n  path.push([tree, key]) // performance: update in place\n  return trackDown(undefined, '', path)\n}\n\nconst lookup = <T = any>(tree: RadixTree<T>, key: string): RadixTree<T> | undefined => {\n  if (key.length === 0 || tree == null) { return tree }\n\n  for (const k of tree.keys()) {\n    if (k !== LEAF && key.startsWith(k)) {\n      return lookup(tree.get(k)!, key.slice(k.length))\n    }\n  }\n}\n\n// Create a path in the radix tree for the given key, and returns the deepest\n// node. This function is in the hot path for indexing. It avoids unnecessary\n// string operations and recursion for performance.\nconst createPath = <T = any>(node: RadixTree<T>, key: string): RadixTree<T> => {\n  const keyLength = key.length\n\n  outer: for (let pos = 0; node && pos < keyLength;) {\n    for (const k of node.keys()) {\n      // Check whether this key is a candidate: the first characters must match.\n      if (k !== LEAF && key[pos] === k[0]) {\n        const len = Math.min(keyLength - pos, k.length)\n\n        // Advance offset to the point where key and k no longer match.\n        let offset = 1\n        while (offset < len && key[pos + offset] === k[offset]) ++offset\n\n        const child = node.get(k)!\n        if (offset === k.length) {\n          // The existing key is shorter than the key we need to create.\n          node = child\n        } else {\n          // Partial match: we need to insert an intermediate node to contain\n          // both the existing subtree and the new node.\n          const intermediate = new Map()\n          intermediate.set(k.slice(offset), child)\n          node.set(key.slice(pos, pos + offset), intermediate)\n          node.delete(k)\n          node = intermediate\n        }\n\n        pos += offset\n        continue outer\n      }\n    }\n\n    // Create a final child node to contain the final suffix of the key.\n    const child = new Map()\n    node.set(key.slice(pos), child)\n    return child\n  }\n\n  return node\n}\n\nconst remove = <T = any>(tree: RadixTree<T>, key: string): void => {\n  const [node, path] = trackDown(tree, key)\n  if (node === undefined) { return }\n  node.delete(LEAF)\n\n  if (node.size === 0) {\n    cleanup(path)\n  } else if (node.size === 1) {\n    const [key, value] = node.entries().next().value!\n    merge(path, key as string, value as RadixTree<T>)\n  }\n}\n\nconst cleanup = <T = any>(path: Path<T>): void => {\n  if (path.length === 0) { return }\n\n  const [node, key] = last(path)\n  node!.delete(key)\n\n  if (node!.size === 0) {\n    cleanup(path.slice(0, -1))\n  } else if (node!.size === 1) {\n    const [key, value] = node!.entries().next().value!\n    if (key !== LEAF) {\n      merge(path.slice(0, -1), key as string, value as RadixTree<T>)\n    }\n  }\n}\n\nconst merge = <T = any>(path: Path<T>, key: string, value: RadixTree<T>): void => {\n  if (path.length === 0) { return }\n\n  const [node, nodeKey] = last(path)\n  node!.set(nodeKey + key, value)\n  node!.delete(nodeKey)\n}\n\nconst last = <T = any>(array: T[]): T => {\n  return array[array.length - 1]\n}\n", "import SearchableMap from './SearchableMap/SearchableMap'\n\nexport type LowercaseCombinationOperator = 'or' | 'and' | 'and_not'\nexport type CombinationOperator = LowercaseCombinationOperator | Uppercase<LowercaseCombinationOperator> | Capitalize<LowercaseCombinationOperator>\n\nconst OR: LowercaseCombinationOperator = 'or'\nconst AND: LowercaseCombinationOperator = 'and'\nconst AND_NOT: LowercaseCombinationOperator = 'and_not'\n\n/**\n * Search options to customize the search behavior.\n */\nexport type SearchOptions = {\n  /**\n   * Names of the fields to search in. If omitted, all fields are searched.\n   */\n  fields?: string[],\n\n  /**\n   * Function used to filter search results, for example on the basis of stored\n   * fields. It takes as argument each search result and should return a boolean\n   * to indicate if the result should be kept or not.\n   */\n  filter?: (result: SearchResult) => boolean,\n\n  /**\n   * Key-value object of field names to boosting values. By default, fields are\n   * assigned a boosting factor of 1. If one assigns to a field a boosting value\n   * of 2, a result that matches the query in that field is assigned a score\n   * twice as high as a result matching the query in another field, all else\n   * being equal.\n   */\n  boost?: { [fieldName: string]: number },\n\n  /**\n   * Function to calculate a boost factor for each term.\n   *\n   * This function, if provided, is called for each query term (as split by\n   * `tokenize` and processed by `processTerm`). The arguments passed to the\n   * function are the query term, the positional index of the term in the query,\n   * and the array of all query terms. It is expected to return a numeric boost\n   * factor for the term. A factor lower than 1 reduces the importance of the\n   * term, a factor greater than 1 increases it. A factor of exactly 1 is\n   * neutral, and does not affect the term's importance.\n   */\n  boostTerm?: (term: string, i: number, terms: string[]) => number,\n\n  /**\n   * Relative weights to assign to prefix search results and fuzzy search\n   * results. Exact matches are assigned a weight of 1.\n   */\n  weights?: { fuzzy: number, prefix: number },\n\n  /**\n   * Function to calculate a boost factor for documents. It takes as arguments\n   * the document ID, and a term that matches the search in that document, and\n   * the value of the stored fields for the document (if any).  It should return\n   * a boosting factor: a number higher than 1 increases the computed score, a\n   * number lower than 1 decreases the score, and a falsy value skips the search\n   * result completely.\n   */\n  boostDocument?: (documentId: any, term: string, storedFields?: Record<string, unknown>) => number,\n\n  /**\n   * Controls whether to perform prefix search. It can be a simple boolean, or a\n   * function.\n   *\n   * If a boolean is passed, prefix search is performed if true.\n   *\n   * If a function is passed, it is called upon search with a search term, the\n   * positional index of that search term in the tokenized search query, and the\n   * tokenized search query. The function should return a boolean to indicate\n   * whether to perform prefix search for that search term.\n   */\n  prefix?: boolean | ((term: string, index: number, terms: string[]) => boolean),\n\n  /**\n   * Controls whether to perform fuzzy search. It can be a simple boolean, or a\n   * number, or a function.\n   *\n   * If a boolean is given, fuzzy search with a default fuzziness parameter is\n   * performed if true.\n   *\n   * If a number higher or equal to 1 is given, fuzzy search is performed, with\n   * a maximum edit distance (Levenshtein) equal to the number.\n   *\n   * If a number between 0 and 1 is given, fuzzy search is performed within a\n   * maximum edit distance corresponding to that fraction of the term length,\n   * approximated to the nearest integer. For example, 0.2 would mean an edit\n   * distance of 20% of the term length, so 1 character in a 5-characters term.\n   * The calculated fuzziness value is limited by the `maxFuzzy` option, to\n   * prevent slowdown for very long queries.\n   *\n   * If a function is passed, the function is called upon search with a search\n   * term, a positional index of that term in the tokenized search query, and\n   * the tokenized search query. It should return a boolean or a number, with\n   * the meaning documented above.\n   */\n  fuzzy?: boolean | number | ((term: string, index: number, terms: string[]) => boolean | number),\n\n  /**\n   * Controls the maximum fuzziness when using a fractional fuzzy value. This is\n   * set to 6 by default. Very high edit distances usually don't produce\n   * meaningful results, but can excessively impact search performance.\n   */\n  maxFuzzy?: number,\n\n  /**\n   * The operand to combine partial results for each term. By default it is\n   * \"OR\", so results matching _any_ of the search terms are returned by a\n   * search. If \"AND\" is given, only results matching _all_ the search terms are\n   * returned by a search.\n   */\n  combineWith?: CombinationOperator,\n\n  /**\n   * Function to tokenize the search query. By default, the same tokenizer used\n   * for indexing is used also for search.\n   *\n   * @remarks This function is called after `extractField` extracts a truthy\n   * value from a field. This function is then expected to split the extracted\n   * `text` document into tokens (more commonly referred to as \"terms\" in this\n   * context). The resulting split might be simple, like for example on word\n   * boundaries, or it might be more complex, taking into account certain\n   * encoding, or parsing needs, or even just special cases. Think about how one\n   * might need to go about indexing the term \"short-term\". You would likely\n   * want to treat this case specially, and return two terms instead, `[\n   * \"short\", \"term\" ]`.\n   *\n   * Or, you could let such a case be handled by the `processTerm` function,\n   * which is designed to turn each token/term into whole terms or sub-terms. In\n   * any case, the purpose of this function is to split apart the provided\n   * `text` document into parts that can be processed by the `processTerm`\n   * function.\n   */\n  tokenize?: (text: string) => string[],\n\n  /**\n   * Function to process or normalize terms in the search query. By default, the\n   * same term processor used for indexing is used also for search.\n   *\n   * @remarks\n   * During the document indexing phase, the first step is to call the\n   * `extractField` function to fetch the requested value/field from the\n   * document. This is then passed off to the `tokenize` function, which will\n   * break apart each value into \"terms\". These terms are then individually\n   * passed through this function to compute each term individually. A term\n   * might for example be something like \"lbs\", in which case one would likely\n   * want to return `[ \"lbs\", \"lb\", \"pound\", \"pounds\" ]`. You may also return\n   * just a single string, or a falsy value if you would like to skip indexing\n   * entirely for a specific term.\n   *\n   * Truthy return value(s) are then fed to the indexer as positive matches for\n   * this document. In our example above, all four of the `[ \"lbs\", \"lb\",\n   * \"pound\", \"pounds\" ]` terms would be added to the indexing engine, matching\n   * against the current document being computed.\n   *\n   * *Note: Whatever values are returned from this function will receive no\n   * further processing before being indexed. This means for example, if you\n   * include whitespace at the beginning or end of a word, it will also be\n   * indexed that way, with the included whitespace.*\n   */\n  processTerm?: (term: string) => string | string[] | null | undefined | false\n\n  /**\n   * BM25+ algorithm parameters. Customizing these is almost never necessary,\n   * and finetuning them requires an understanding of the BM25 scoring model. In\n   * most cases, it is best to omit this option to use defaults, and instead use\n   * boosting to tweak scoring for specific use cases.\n   */\n  bm25?: BM25Params\n}\n\ntype SearchOptionsWithDefaults = SearchOptions & {\n  boost: { [fieldName: string]: number },\n\n  weights: { fuzzy: number, prefix: number },\n\n  prefix: boolean | ((term: string, index: number, terms: string[]) => boolean),\n\n  fuzzy: boolean | number | ((term: string, index: number, terms: string[]) => boolean | number),\n\n  maxFuzzy: number,\n\n  combineWith: CombinationOperator\n\n  bm25: BM25Params\n}\n\n/**\n * Configuration options passed to the {@link MiniSearch} constructor\n *\n * @typeParam T  The type of documents being indexed.\n */\nexport type Options<T = any> = {\n   /**\n    * Names of the document fields to be indexed.\n    */\n  fields: string[],\n\n   /**\n    * Name of the ID field, uniquely identifying a document.\n    */\n  idField?: string,\n\n   /**\n    * Names of fields to store, so that search results would include them. By\n    * default none, so results would only contain the id field.\n    */\n  storeFields?: string[],\n\n   /**\n    * Function used to extract the value of each field in documents. By default,\n    * the documents are assumed to be plain objects with field names as keys,\n    * but by specifying a custom `extractField` function one can completely\n    * customize how the fields are extracted.\n    *\n    * The function takes as arguments the document, and the name of the field to\n    * extract from it. It should return the field value as a string.\n    *\n    * @remarks\n    * The returned string is fed into the `tokenize` function to split it up\n    * into tokens.\n    */\n  extractField?: (document: T, fieldName: string) => string,\n\n   /**\n    * Function used to split a field value into individual terms to be indexed.\n    * The default tokenizer separates terms by space or punctuation, but a\n    * custom tokenizer can be provided for custom logic.\n    *\n    * The function takes as arguments string to tokenize, and the name of the\n    * field it comes from. It should return the terms as an array of strings.\n    * When used for tokenizing a search query instead of a document field, the\n    * `fieldName` is undefined.\n    *\n    * @remarks\n    * This function is called after `extractField` extracts a truthy value from a\n    * field. This function is then expected to split the extracted `text` document\n    * into tokens (more commonly referred to as \"terms\" in this context). The resulting\n    * split might be simple, like for example on word boundaries, or it might be more\n    * complex, taking into account certain encoding, or parsing needs, or even just\n    * special cases. Think about how one might need to go about indexing the term\n    * \"short-term\". You would likely want to treat this case specially, and return two\n    * terms instead, `[ \"short\", \"term\" ]`.\n    *\n    * Or, you could let such a case be handled by the `processTerm` function,\n    * which is designed to turn each token/term into whole terms or sub-terms. In any\n    * case, the purpose of this function is to split apart the provided `text` document\n    * into parts that can be processed by the `processTerm` function.\n    */\n  tokenize?: (text: string, fieldName?: string) => string[],\n\n   /**\n    * Function used to process a term before indexing or search. This can be\n    * used for normalization (such as stemming). By default, terms are\n    * downcased, and otherwise no other normalization is performed.\n    *\n    * The function takes as arguments a term to process, and the name of the\n    * field it comes from. It should return the processed term as a string, or a\n    * falsy value to reject the term entirely.\n    *\n    * It can also return an array of strings, in which case each string in the\n    * returned array is indexed as a separate term.\n    *\n    * @remarks\n    * During the document indexing phase, the first step is to call the `extractField`\n    * function to fetch the requested value/field from the document. This is then\n    * passed off to the `tokenizer`, which will break apart each value into \"terms\".\n    * These terms are then individually passed through the `processTerm` function\n    * to compute each term individually. A term might for example be something\n    * like \"lbs\", in which case one would likely want to return\n    * `[ \"lbs\", \"lb\", \"pound\", \"pounds\" ]`. You may also return a single string value,\n    * or a falsy value if you would like to skip indexing entirely for a specific term.\n    *\n    * Truthy return value(s) are then fed to the indexer as positive matches for this\n    * document. In our example above, all four of the `[ \"lbs\", \"lb\", \"pound\", \"pounds\" ]`\n    * terms would be added to the indexing engine, matching against the current document\n    * being computed.\n    *\n    * *Note: Whatever values are returned from this function will receive no further\n    * processing before being indexed. This means for example, if you include whitespace\n    * at the beginning or end of a word, it will also be indexed that way, with the\n    * included whitespace.*\n    */\n  processTerm?: (term: string, fieldName?: string) => string | string[] | null | undefined | false,\n\n  /**\n   * Function called to log messages. Arguments are a log level ('debug',\n   * 'info', 'warn', or 'error'), a log message, and an optional string code\n   * that identifies the reason for the log.\n   *\n   * The default implementation uses `console`, if defined.\n   */\n  logger?: (level: LogLevel, message: string, code?: string) => void\n\n  /**\n   * If `true` (the default), vacuuming is performed automatically as soon as\n   * {@link MiniSearch#discard} is called a certain number of times, cleaning up\n   * obsolete references from the index. If `false`, no automatic vacuuming is\n   * performed. Custom settings controlling auto vacuuming thresholds, as well\n   * as batching behavior, can be passed as an object (see the {@link\n   * AutoVacuumOptions} type).\n   */\n  autoVacuum?: boolean | AutoVacuumOptions\n\n   /**\n    * Default search options (see the {@link SearchOptions} type and the {@link\n    * MiniSearch#search} method for details)\n    */\n  searchOptions?: SearchOptions,\n\n   /**\n    * Default auto suggest options (see the {@link SearchOptions} type and the\n    * {@link MiniSearch#autoSuggest} method for details)\n    */\n  autoSuggestOptions?: SearchOptions\n}\n\ntype OptionsWithDefaults<T = any> = Options<T> & {\n  storeFields: string[]\n\n  idField: string\n\n  extractField: (document: T, fieldName: string) => string\n\n  tokenize: (text: string, fieldName: string) => string[]\n\n  processTerm: (term: string, fieldName: string) => string | string[] | null | undefined | false\n\n  logger: (level: LogLevel, message: string, code?: string) => void\n\n  autoVacuum: false | AutoVacuumOptions\n\n  searchOptions: SearchOptionsWithDefaults\n\n  autoSuggestOptions: SearchOptions\n}\n\ntype LogLevel = 'debug' | 'info' | 'warn' | 'error'\n\n/**\n * The type of auto-suggestions\n */\nexport type Suggestion = {\n  /**\n   * The suggestion\n   */\n  suggestion: string,\n\n  /**\n   * Suggestion as an array of terms\n   */\n  terms: string[],\n\n  /**\n   * Score for the suggestion\n   */\n  score: number\n}\n\n/**\n * Match information for a search result. It is a key-value object where keys\n * are terms that matched, and values are the list of fields that the term was\n * found in.\n */\nexport type MatchInfo = {\n  [term: string]: string[]\n}\n\n/**\n * Type of the search results. Each search result indicates the document ID, the\n * terms that matched, the match information, the score, and all the stored\n * fields.\n */\nexport type SearchResult = {\n  /**\n   * The document ID\n   */\n  id: any,\n\n  /**\n   * List of document terms that matched. For example, if a prefix search for\n   * `\"moto\"` matches `\"motorcycle\"`, `terms` will contain `\"motorcycle\"`.\n   */\n  terms: string[],\n\n  /**\n   * List of query terms that matched. For example, if a prefix search for\n   * `\"moto\"` matches `\"motorcycle\"`, `queryTerms` will contain `\"moto\"`.\n   */\n  queryTerms: string[],\n\n  /**\n   * Score of the search results\n   */\n  score: number,\n\n  /**\n   * Match information, see {@link MatchInfo}\n   */\n  match: MatchInfo,\n\n  /**\n   * Stored fields\n   */\n  [key: string]: any\n}\n\n/**\n * @ignore\n */\nexport type AsPlainObject = {\n  documentCount: number,\n  nextId: number,\n  documentIds: { [shortId: string]: any }\n  fieldIds: { [fieldName: string]: number }\n  fieldLength: { [shortId: string]: number[] }\n  averageFieldLength: number[],\n  storedFields: { [shortId: string]: any }\n  dirtCount?: number,\n  index: [string, { [fieldId: string]: SerializedIndexEntry }][]\n  serializationVersion: number\n}\n\nexport type QueryCombination = SearchOptions & { queries: Query[] }\n\n/**\n * Wildcard query, used to match all terms\n */\nexport type Wildcard = typeof MiniSearch.wildcard\n\n/**\n * Search query expression, either a query string or an expression tree\n * combining several queries with a combination of AND or OR.\n */\nexport type Query = QueryCombination | string | Wildcard\n\n/**\n * Options to control vacuuming behavior.\n *\n * Vacuuming cleans up document references made obsolete by {@link\n * MiniSearch.discard} from the index. On large indexes, vacuuming is\n * potentially costly, because it has to traverse the whole inverted index.\n * Therefore, in order to dilute this cost so it does not negatively affects the\n * application, vacuuming is performed in batches, with a delay between each\n * batch. These options are used to configure the batch size and the delay\n * between batches.\n */\nexport type VacuumOptions = {\n  /**\n   * Size of each vacuuming batch (the number of terms in the index that will be\n   * traversed in each batch). Defaults to 1000.\n   */\n  batchSize?: number,\n\n  /**\n   * Wait time between each vacuuming batch in milliseconds. Defaults to 10.\n   */\n  batchWait?: number\n}\n\n/**\n * Sets minimum thresholds for `dirtCount` and `dirtFactor` that trigger an\n * automatic vacuuming.\n */\nexport type VacuumConditions = {\n  /**\n   * Minimum `dirtCount` (number of discarded documents since the last vacuuming)\n   * under which auto vacuum is not triggered. It defaults to 20.\n   */\n  minDirtCount?: number\n\n  /**\n   * Minimum `dirtFactor` (proportion of discarded documents over the total)\n   * under which auto vacuum is not triggered. It defaults to 0.1.\n   */\n  minDirtFactor?: number,\n}\n\n/**\n * Options to control auto vacuum behavior. When discarding a document with\n * {@link MiniSearch#discard}, a vacuuming operation is automatically started if\n * the `dirtCount` and `dirtFactor` are above the `minDirtCount` and\n * `minDirtFactor` thresholds defined by this configuration. See {@link\n * VacuumConditions} for details on these.\n *\n * Also, `batchSize` and `batchWait` can be specified, controlling batching\n * behavior (see {@link VacuumOptions}).\n */\nexport type AutoVacuumOptions = VacuumOptions & VacuumConditions\n\ntype QuerySpec = {\n  prefix: boolean,\n  fuzzy: number | boolean,\n  term: string,\n  termBoost: number\n}\n\ntype DocumentTermFreqs = Map<number, number>\ntype FieldTermData = Map<number, DocumentTermFreqs>\n\ninterface RawResultValue {\n  // Intermediate score, before applying the final score based on number of\n  // matched terms.\n  score: number,\n\n  // Set of all query terms that were matched. They may not be present in the\n  // text exactly in the case of prefix/fuzzy matches. We must check for\n  // uniqueness before adding a new term. This is much faster than using a set,\n  // because the number of elements is relatively small.\n  terms: string[],\n\n  // All terms that were found in the content, including the fields in which\n  // they were present. This object will be provided as part of the final search\n  // results.\n  match: MatchInfo,\n}\n\ntype RawResult = Map<number, RawResultValue>\n\n/**\n * {@link MiniSearch} is the main entrypoint class, implementing a full-text\n * search engine in memory.\n *\n * @typeParam T  The type of the documents being indexed.\n *\n * ### Basic example:\n *\n * ```javascript\n * const documents = [\n *   {\n *     id: 1,\n *     title: 'Moby Dick',\n *     text: 'Call me Ishmael. Some years ago...',\n *     category: 'fiction'\n *   },\n *   {\n *     id: 2,\n *     title: 'Zen and the Art of Motorcycle Maintenance',\n *     text: 'I can see by my watch...',\n *     category: 'fiction'\n *   },\n *   {\n *     id: 3,\n *     title: 'Neuromancer',\n *     text: 'The sky above the port was...',\n *     category: 'fiction'\n *   },\n *   {\n *     id: 4,\n *     title: 'Zen and the Art of Archery',\n *     text: 'At first sight it must seem...',\n *     category: 'non-fiction'\n *   },\n *   // ...and more\n * ]\n *\n * // Create a search engine that indexes the 'title' and 'text' fields for\n * // full-text search. Search results will include 'title' and 'category' (plus the\n * // id field, that is always stored and returned)\n * const miniSearch = new MiniSearch({\n *   fields: ['title', 'text'],\n *   storeFields: ['title', 'category']\n * })\n *\n * // Add documents to the index\n * miniSearch.addAll(documents)\n *\n * // Search for documents:\n * let results = miniSearch.search('zen art motorcycle')\n * // => [\n * //   { id: 2, title: 'Zen and the Art of Motorcycle Maintenance', category: 'fiction', score: 2.77258 },\n * //   { id: 4, title: 'Zen and the Art of Archery', category: 'non-fiction', score: 1.38629 }\n * // ]\n * ```\n */\nexport default class MiniSearch<T = any> {\n  protected _options: OptionsWithDefaults<T>\n  protected _index: SearchableMap<FieldTermData>\n  protected _documentCount: number\n  protected _documentIds: Map<number, any>\n  protected _idToShortId: Map<any, number>\n  protected _fieldIds: { [key: string]: number }\n  protected _fieldLength: Map<number, number[]>\n  protected _avgFieldLength: number[]\n  protected _nextId: number\n  protected _storedFields: Map<number, Record<string, unknown>>\n  protected _dirtCount: number\n  private _currentVacuum: Promise<void> | null\n  private _enqueuedVacuum: Promise<void> | null\n  private _enqueuedVacuumConditions: VacuumConditions | undefined\n\n  /**\n   * The special wildcard symbol that can be passed to {@link MiniSearch#search}\n   * to match all documents\n   */\n  static readonly wildcard: unique symbol = Symbol('*')\n\n  /**\n   * @param options  Configuration options\n   *\n   * ### Examples:\n   *\n   * ```javascript\n   * // Create a search engine that indexes the 'title' and 'text' fields of your\n   * // documents:\n   * const miniSearch = new MiniSearch({ fields: ['title', 'text'] })\n   * ```\n   *\n   * ### ID Field:\n   *\n   * ```javascript\n   * // Your documents are assumed to include a unique 'id' field, but if you want\n   * // to use a different field for document identification, you can set the\n   * // 'idField' option:\n   * const miniSearch = new MiniSearch({ idField: 'key', fields: ['title', 'text'] })\n   * ```\n   *\n   * ### Options and defaults:\n   *\n   * ```javascript\n   * // The full set of options (here with their default value) is:\n   * const miniSearch = new MiniSearch({\n   *   // idField: field that uniquely identifies a document\n   *   idField: 'id',\n   *\n   *   // extractField: function used to get the value of a field in a document.\n   *   // By default, it assumes the document is a flat object with field names as\n   *   // property keys and field values as string property values, but custom logic\n   *   // can be implemented by setting this option to a custom extractor function.\n   *   extractField: (document, fieldName) => document[fieldName],\n   *\n   *   // tokenize: function used to split fields into individual terms. By\n   *   // default, it is also used to tokenize search queries, unless a specific\n   *   // `tokenize` search option is supplied. When tokenizing an indexed field,\n   *   // the field name is passed as the second argument.\n   *   tokenize: (string, _fieldName) => string.split(SPACE_OR_PUNCTUATION),\n   *\n   *   // processTerm: function used to process each tokenized term before\n   *   // indexing. It can be used for stemming and normalization. Return a falsy\n   *   // value in order to discard a term. By default, it is also used to process\n   *   // search queries, unless a specific `processTerm` option is supplied as a\n   *   // search option. When processing a term from a indexed field, the field\n   *   // name is passed as the second argument.\n   *   processTerm: (term, _fieldName) => term.toLowerCase(),\n   *\n   *   // searchOptions: default search options, see the `search` method for\n   *   // details\n   *   searchOptions: undefined,\n   *\n   *   // fields: document fields to be indexed. Mandatory, but not set by default\n   *   fields: undefined\n   *\n   *   // storeFields: document fields to be stored and returned as part of the\n   *   // search results.\n   *   storeFields: []\n   * })\n   * ```\n   */\n  constructor (options: Options<T>) {\n    if (options?.fields == null) {\n      throw new Error('MiniSearch: option \"fields\" must be provided')\n    }\n\n    const autoVacuum = (options.autoVacuum == null || options.autoVacuum === true) ? defaultAutoVacuumOptions : options.autoVacuum\n\n    this._options = {\n      ...defaultOptions,\n      ...options,\n      autoVacuum,\n      searchOptions: { ...defaultSearchOptions, ...(options.searchOptions || {}) },\n      autoSuggestOptions: { ...defaultAutoSuggestOptions, ...(options.autoSuggestOptions || {}) }\n    }\n\n    this._index = new SearchableMap()\n\n    this._documentCount = 0\n\n    this._documentIds = new Map()\n\n    this._idToShortId = new Map()\n\n    // Fields are defined during initialization, don't change, are few in\n    // number, rarely need iterating over, and have string keys. Therefore in\n    // this case an object is a better candidate than a Map to store the mapping\n    // from field key to ID.\n    this._fieldIds = {}\n\n    this._fieldLength = new Map()\n\n    this._avgFieldLength = []\n\n    this._nextId = 0\n\n    this._storedFields = new Map()\n\n    this._dirtCount = 0\n\n    this._currentVacuum = null\n\n    this._enqueuedVacuum = null\n    this._enqueuedVacuumConditions = defaultVacuumConditions\n\n    this.addFields(this._options.fields)\n  }\n\n  /**\n   * Adds a document to the index\n   *\n   * @param document  The document to be indexed\n   */\n  add (document: T): void {\n    const { extractField, tokenize, processTerm, fields, idField } = this._options\n    const id = extractField(document, idField)\n    if (id == null) {\n      throw new Error(`MiniSearch: document does not have ID field \"${idField}\"`)\n    }\n\n    if (this._idToShortId.has(id)) {\n      throw new Error(`MiniSearch: duplicate ID ${id}`)\n    }\n\n    const shortDocumentId = this.addDocumentId(id)\n    this.saveStoredFields(shortDocumentId, document)\n\n    for (const field of fields) {\n      const fieldValue = extractField(document, field)\n      if (fieldValue == null) continue\n\n      const tokens = tokenize(fieldValue.toString(), field)\n      const fieldId = this._fieldIds[field]\n\n      const uniqueTerms = new Set(tokens).size\n      this.addFieldLength(shortDocumentId, fieldId, this._documentCount - 1, uniqueTerms)\n\n      for (const term of tokens) {\n        const processedTerm = processTerm(term, field)\n        if (Array.isArray(processedTerm)) {\n          for (const t of processedTerm) {\n            this.addTerm(fieldId, shortDocumentId, t)\n          }\n        } else if (processedTerm) {\n          this.addTerm(fieldId, shortDocumentId, processedTerm)\n        }\n      }\n    }\n  }\n\n  /**\n   * Adds all the given documents to the index\n   *\n   * @param documents  An array of documents to be indexed\n   */\n  addAll (documents: readonly T[]): void {\n    for (const document of documents) this.add(document)\n  }\n\n  /**\n   * Adds all the given documents to the index asynchronously.\n   *\n   * Returns a promise that resolves (to `undefined`) when the indexing is done.\n   * This method is useful when index many documents, to avoid blocking the main\n   * thread. The indexing is performed asynchronously and in chunks.\n   *\n   * @param documents  An array of documents to be indexed\n   * @param options  Configuration options\n   * @return A promise resolving to `undefined` when the indexing is done\n   */\n  addAllAsync (documents: readonly T[], options: { chunkSize?: number } = {}): Promise<void> {\n    const { chunkSize = 10 } = options\n    const acc: { chunk: T[], promise: Promise<void> } = { chunk: [], promise: Promise.resolve() }\n\n    const { chunk, promise } = documents.reduce(({ chunk, promise }, document: T, i: number) => {\n      chunk.push(document)\n      if ((i + 1) % chunkSize === 0) {\n        return {\n          chunk: [],\n          promise: promise\n            .then(() => new Promise(resolve => setTimeout(resolve, 0)))\n            .then(() => this.addAll(chunk))\n        }\n      } else {\n        return { chunk, promise }\n      }\n    }, acc)\n\n    return promise.then(() => this.addAll(chunk))\n  }\n\n  /**\n   * Removes the given document from the index.\n   *\n   * The document to remove must NOT have changed between indexing and removal,\n   * otherwise the index will be corrupted.\n   *\n   * This method requires passing the full document to be removed (not just the\n   * ID), and immediately removes the document from the inverted index, allowing\n   * memory to be released. A convenient alternative is {@link\n   * MiniSearch#discard}, which needs only the document ID, and has the same\n   * visible effect, but delays cleaning up the index until the next vacuuming.\n   *\n   * @param document  The document to be removed\n   */\n  remove (document: T): void {\n    const { tokenize, processTerm, extractField, fields, idField } = this._options\n    const id = extractField(document, idField)\n\n    if (id == null) {\n      throw new Error(`MiniSearch: document does not have ID field \"${idField}\"`)\n    }\n\n    const shortId = this._idToShortId.get(id)\n\n    if (shortId == null) {\n      throw new Error(`MiniSearch: cannot remove document with ID ${id}: it is not in the index`)\n    }\n\n    for (const field of fields) {\n      const fieldValue = extractField(document, field)\n      if (fieldValue == null) continue\n\n      const tokens = tokenize(fieldValue.toString(), field)\n      const fieldId = this._fieldIds[field]\n\n      const uniqueTerms = new Set(tokens).size\n      this.removeFieldLength(shortId, fieldId, this._documentCount, uniqueTerms)\n\n      for (const term of tokens) {\n        const processedTerm = processTerm(term, field)\n        if (Array.isArray(processedTerm)) {\n          for (const t of processedTerm) {\n            this.removeTerm(fieldId, shortId, t)\n          }\n        } else if (processedTerm) {\n          this.removeTerm(fieldId, shortId, processedTerm)\n        }\n      }\n    }\n\n    this._storedFields.delete(shortId)\n    this._documentIds.delete(shortId)\n    this._idToShortId.delete(id)\n    this._fieldLength.delete(shortId)\n    this._documentCount -= 1\n  }\n\n  /**\n   * Removes all the given documents from the index. If called with no arguments,\n   * it removes _all_ documents from the index.\n   *\n   * @param documents  The documents to be removed. If this argument is omitted,\n   * all documents are removed. Note that, for removing all documents, it is\n   * more efficient to call this method with no arguments than to pass all\n   * documents.\n   */\n  removeAll (documents?: readonly T[]): void {\n    if (documents) {\n      for (const document of documents) this.remove(document)\n    } else if (arguments.length > 0) {\n      throw new Error('Expected documents to be present. Omit the argument to remove all documents.')\n    } else {\n      this._index = new SearchableMap()\n      this._documentCount = 0\n      this._documentIds = new Map()\n      this._idToShortId = new Map()\n      this._fieldLength = new Map()\n      this._avgFieldLength = []\n      this._storedFields = new Map()\n      this._nextId = 0\n    }\n  }\n\n  /**\n   * Discards the document with the given ID, so it won't appear in search results\n   *\n   * It has the same visible effect of {@link MiniSearch.remove} (both cause the\n   * document to stop appearing in searches), but a different effect on the\n   * internal data structures:\n   *\n   *   - {@link MiniSearch#remove} requires passing the full document to be\n   *   removed as argument, and removes it from the inverted index immediately.\n   *\n   *   - {@link MiniSearch#discard} instead only needs the document ID, and\n   *   works by marking the current version of the document as discarded, so it\n   *   is immediately ignored by searches. This is faster and more convenient\n   *   than {@link MiniSearch#remove}, but the index is not immediately\n   *   modified. To take care of that, vacuuming is performed after a certain\n   *   number of documents are discarded, cleaning up the index and allowing\n   *   memory to be released.\n   *\n   * After discarding a document, it is possible to re-add a new version, and\n   * only the new version will appear in searches. In other words, discarding\n   * and re-adding a document works exactly like removing and re-adding it. The\n   * {@link MiniSearch.replace} method can also be used to replace a document\n   * with a new version.\n   *\n   * #### Details about vacuuming\n   *\n   * Repetite calls to this method would leave obsolete document references in\n   * the index, invisible to searches. Two mechanisms take care of cleaning up:\n   * clean up during search, and vacuuming.\n   *\n   *   - Upon search, whenever a discarded ID is found (and ignored for the\n   *   results), references to the discarded document are removed from the\n   *   inverted index entries for the search terms. This ensures that subsequent\n   *   searches for the same terms do not need to skip these obsolete references\n   *   again.\n   *\n   *   - In addition, vacuuming is performed automatically by default (see the\n   *   `autoVacuum` field in {@link Options}) after a certain number of\n   *   documents are discarded. Vacuuming traverses all terms in the index,\n   *   cleaning up all references to discarded documents. Vacuuming can also be\n   *   triggered manually by calling {@link MiniSearch#vacuum}.\n   *\n   * @param id  The ID of the document to be discarded\n   */\n  discard (id: any): void {\n    const shortId = this._idToShortId.get(id)\n\n    if (shortId == null) {\n      throw new Error(`MiniSearch: cannot discard document with ID ${id}: it is not in the index`)\n    }\n\n    this._idToShortId.delete(id)\n    this._documentIds.delete(shortId)\n    this._storedFields.delete(shortId)\n\n    ;(this._fieldLength.get(shortId) || []).forEach((fieldLength, fieldId) => {\n      this.removeFieldLength(shortId, fieldId, this._documentCount, fieldLength)\n    })\n\n    this._fieldLength.delete(shortId)\n\n    this._documentCount -= 1\n    this._dirtCount += 1\n\n    this.maybeAutoVacuum()\n  }\n\n  private maybeAutoVacuum (): void {\n    if (this._options.autoVacuum === false) { return }\n\n    const { minDirtFactor, minDirtCount, batchSize, batchWait } = this._options.autoVacuum\n    this.conditionalVacuum({ batchSize, batchWait }, { minDirtCount, minDirtFactor })\n  }\n\n  /**\n   * Discards the documents with the given IDs, so they won't appear in search\n   * results\n   *\n   * It is equivalent to calling {@link MiniSearch#discard} for all the given\n   * IDs, but with the optimization of triggering at most one automatic\n   * vacuuming at the end.\n   *\n   * Note: to remove all documents from the index, it is faster and more\n   * convenient to call {@link MiniSearch.removeAll} with no argument, instead\n   * of passing all IDs to this method.\n   */\n  discardAll (ids: readonly any[]): void {\n    const autoVacuum = this._options.autoVacuum\n\n    try {\n      this._options.autoVacuum = false\n\n      for (const id of ids) {\n        this.discard(id)\n      }\n    } finally {\n      this._options.autoVacuum = autoVacuum\n    }\n\n    this.maybeAutoVacuum()\n  }\n\n  /**\n   * It replaces an existing document with the given updated version\n   *\n   * It works by discarding the current version and adding the updated one, so\n   * it is functionally equivalent to calling {@link MiniSearch#discard}\n   * followed by {@link MiniSearch#add}. The ID of the updated document should\n   * be the same as the original one.\n   *\n   * Since it uses {@link MiniSearch#discard} internally, this method relies on\n   * vacuuming to clean up obsolete document references from the index, allowing\n   * memory to be released (see {@link MiniSearch#discard}).\n   *\n   * @param updatedDocument  The updated document to replace the old version\n   * with\n   */\n  replace (updatedDocument: T): void {\n    const { idField, extractField } = this._options\n    const id = extractField(updatedDocument, idField)\n\n    this.discard(id)\n    this.add(updatedDocument)\n  }\n\n  /**\n   * Triggers a manual vacuuming, cleaning up references to discarded documents\n   * from the inverted index\n   *\n   * Vacuuming is only useful for applications that use the {@link\n   * MiniSearch#discard} or {@link MiniSearch#replace} methods.\n   *\n   * By default, vacuuming is performed automatically when needed (controlled by\n   * the `autoVacuum` field in {@link Options}), so there is usually no need to\n   * call this method, unless one wants to make sure to perform vacuuming at a\n   * specific moment.\n   *\n   * Vacuuming traverses all terms in the inverted index in batches, and cleans\n   * up references to discarded documents from the posting list, allowing memory\n   * to be released.\n   *\n   * The method takes an optional object as argument with the following keys:\n   *\n   *   - `batchSize`: the size of each batch (1000 by default)\n   *\n   *   - `batchWait`: the number of milliseconds to wait between batches (10 by\n   *   default)\n   *\n   * On large indexes, vacuuming could have a non-negligible cost: batching\n   * avoids blocking the thread for long, diluting this cost so that it is not\n   * negatively affecting the application. Nonetheless, this method should only\n   * be called when necessary, and relying on automatic vacuuming is usually\n   * better.\n   *\n   * It returns a promise that resolves (to undefined) when the clean up is\n   * completed. If vacuuming is already ongoing at the time this method is\n   * called, a new one is enqueued immediately after the ongoing one, and a\n   * corresponding promise is returned. However, no more than one vacuuming is\n   * enqueued on top of the ongoing one, even if this method is called more\n   * times (enqueuing multiple ones would be useless).\n   *\n   * @param options  Configuration options for the batch size and delay. See\n   * {@link VacuumOptions}.\n   */\n  vacuum (options: VacuumOptions = {}): Promise<void> {\n    return this.conditionalVacuum(options)\n  }\n\n  private conditionalVacuum (options: VacuumOptions, conditions?: VacuumConditions): Promise<void> {\n    // If a vacuum is already ongoing, schedule another as soon as it finishes,\n    // unless there's already one enqueued. If one was already enqueued, do not\n    // enqueue another on top, but make sure that the conditions are the\n    // broadest.\n    if (this._currentVacuum) {\n      this._enqueuedVacuumConditions = this._enqueuedVacuumConditions && conditions\n      if (this._enqueuedVacuum != null) { return this._enqueuedVacuum }\n\n      this._enqueuedVacuum = this._currentVacuum.then(() => {\n        const conditions = this._enqueuedVacuumConditions\n        this._enqueuedVacuumConditions = defaultVacuumConditions\n        return this.performVacuuming(options, conditions)\n      })\n      return this._enqueuedVacuum\n    }\n\n    if (this.vacuumConditionsMet(conditions) === false) { return Promise.resolve() }\n\n    this._currentVacuum = this.performVacuuming(options)\n    return this._currentVacuum\n  }\n\n  private async performVacuuming (options: VacuumOptions, conditions?: VacuumConditions): Promise<void> {\n    const initialDirtCount = this._dirtCount\n\n    if (this.vacuumConditionsMet(conditions)) {\n      const batchSize = options.batchSize || defaultVacuumOptions.batchSize\n      const batchWait = options.batchWait || defaultVacuumOptions.batchWait\n      let i = 1\n\n      for (const [term, fieldsData] of this._index) {\n        for (const [fieldId, fieldIndex] of fieldsData) {\n          for (const [shortId] of fieldIndex) {\n            if (this._documentIds.has(shortId)) { continue }\n\n            if (fieldIndex.size <= 1) {\n              fieldsData.delete(fieldId)\n            } else {\n              fieldIndex.delete(shortId)\n            }\n          }\n        }\n\n        if (this._index.get(term)!.size === 0) {\n          this._index.delete(term)\n        }\n\n        if (i % batchSize === 0) {\n          await new Promise((resolve) => setTimeout(resolve, batchWait))\n        }\n\n        i += 1\n      }\n\n      this._dirtCount -= initialDirtCount\n    }\n\n    // Make the next lines always async, so they execute after this function returns\n    await null\n\n    this._currentVacuum = this._enqueuedVacuum\n    this._enqueuedVacuum = null\n  }\n\n  private vacuumConditionsMet (conditions?: VacuumConditions) {\n    if (conditions == null) { return true }\n\n    let { minDirtCount, minDirtFactor } = conditions\n    minDirtCount = minDirtCount || defaultAutoVacuumOptions.minDirtCount\n    minDirtFactor = minDirtFactor || defaultAutoVacuumOptions.minDirtFactor\n\n    return this.dirtCount >= minDirtCount && this.dirtFactor >= minDirtFactor\n  }\n\n  /**\n   * Is `true` if a vacuuming operation is ongoing, `false` otherwise\n   */\n  get isVacuuming (): boolean {\n    return this._currentVacuum != null\n  }\n\n  /**\n   * The number of documents discarded since the most recent vacuuming\n   */\n  get dirtCount (): number {\n    return this._dirtCount\n  }\n\n  /**\n   * A number between 0 and 1 giving an indication about the proportion of\n   * documents that are discarded, and can therefore be cleaned up by vacuuming.\n   * A value close to 0 means that the index is relatively clean, while a higher\n   * value means that the index is relatively dirty, and vacuuming could release\n   * memory.\n   */\n  get dirtFactor (): number {\n    return this._dirtCount / (1 + this._documentCount + this._dirtCount)\n  }\n\n  /**\n   * Returns `true` if a document with the given ID is present in the index and\n   * available for search, `false` otherwise\n   *\n   * @param id  The document ID\n   */\n  has (id: any): boolean {\n    return this._idToShortId.has(id)\n  }\n\n  /**\n   * Returns the stored fields (as configured in the `storeFields` constructor\n   * option) for the given document ID. Returns `undefined` if the document is\n   * not present in the index.\n   *\n   * @param id  The document ID\n   */\n  getStoredFields (id: any): Record<string, unknown> | undefined {\n    const shortId = this._idToShortId.get(id)\n\n    if (shortId == null) { return undefined }\n\n    return this._storedFields.get(shortId)\n  }\n\n  /**\n   * Search for documents matching the given search query.\n   *\n   * The result is a list of scored document IDs matching the query, sorted by\n   * descending score, and each including data about which terms were matched and\n   * in which fields.\n   *\n   * ### Basic usage:\n   *\n   * ```javascript\n   * // Search for \"zen art motorcycle\" with default options: terms have to match\n   * // exactly, and individual terms are joined with OR\n   * miniSearch.search('zen art motorcycle')\n   * // => [ { id: 2, score: 2.77258, match: { ... } }, { id: 4, score: 1.38629, match: { ... } } ]\n   * ```\n   *\n   * ### Restrict search to specific fields:\n   *\n   * ```javascript\n   * // Search only in the 'title' field\n   * miniSearch.search('zen', { fields: ['title'] })\n   * ```\n   *\n   * ### Field boosting:\n   *\n   * ```javascript\n   * // Boost a field\n   * miniSearch.search('zen', { boost: { title: 2 } })\n   * ```\n   *\n   * ### Prefix search:\n   *\n   * ```javascript\n   * // Search for \"moto\" with prefix search (it will match documents\n   * // containing terms that start with \"moto\" or \"neuro\")\n   * miniSearch.search('moto neuro', { prefix: true })\n   * ```\n   *\n   * ### Fuzzy search:\n   *\n   * ```javascript\n   * // Search for \"ismael\" with fuzzy search (it will match documents containing\n   * // terms similar to \"ismael\", with a maximum edit distance of 0.2 term.length\n   * // (rounded to nearest integer)\n   * miniSearch.search('ismael', { fuzzy: 0.2 })\n   * ```\n   *\n   * ### Combining strategies:\n   *\n   * ```javascript\n   * // Mix of exact match, prefix search, and fuzzy search\n   * miniSearch.search('ismael mob', {\n   *  prefix: true,\n   *  fuzzy: 0.2\n   * })\n   * ```\n   *\n   * ### Advanced prefix and fuzzy search:\n   *\n   * ```javascript\n   * // Perform fuzzy and prefix search depending on the search term. Here\n   * // performing prefix and fuzzy search only on terms longer than 3 characters\n   * miniSearch.search('ismael mob', {\n   *  prefix: term => term.length > 3\n   *  fuzzy: term => term.length > 3 ? 0.2 : null\n   * })\n   * ```\n   *\n   * ### Combine with AND:\n   *\n   * ```javascript\n   * // Combine search terms with AND (to match only documents that contain both\n   * // \"motorcycle\" and \"art\")\n   * miniSearch.search('motorcycle art', { combineWith: 'AND' })\n   * ```\n   *\n   * ### Combine with AND_NOT:\n   *\n   * There is also an AND_NOT combinator, that finds documents that match the\n   * first term, but do not match any of the other terms. This combinator is\n   * rarely useful with simple queries, and is meant to be used with advanced\n   * query combinations (see later for more details).\n   *\n   * ### Filtering results:\n   *\n   * ```javascript\n   * // Filter only results in the 'fiction' category (assuming that 'category'\n   * // is a stored field)\n   * miniSearch.search('motorcycle art', {\n   *   filter: (result) => result.category === 'fiction'\n   * })\n   * ```\n   *\n   * ### Wildcard query\n   *\n   * Searching for an empty string (assuming the default tokenizer) returns no\n   * results. Sometimes though, one needs to match all documents, like in a\n   * \"wildcard\" search. This is possible by passing the special value\n   * {@link MiniSearch.wildcard} as the query:\n   *\n   * ```javascript\n   * // Return search results for all documents\n   * miniSearch.search(MiniSearch.wildcard)\n   * ```\n   *\n   * Note that search options such as `filter` and `boostDocument` are still\n   * applied, influencing which results are returned, and their order:\n   *\n   * ```javascript\n   * // Return search results for all documents in the 'fiction' category\n   * miniSearch.search(MiniSearch.wildcard, {\n   *   filter: (result) => result.category === 'fiction'\n   * })\n   * ```\n   *\n   * ### Advanced combination of queries:\n   *\n   * It is possible to combine different subqueries with OR, AND, and AND_NOT,\n   * and even with different search options, by passing a query expression\n   * tree object as the first argument, instead of a string.\n   *\n   * ```javascript\n   * // Search for documents that contain \"zen\" and (\"motorcycle\" or \"archery\")\n   * miniSearch.search({\n   *   combineWith: 'AND',\n   *   queries: [\n   *     'zen',\n   *     {\n   *       combineWith: 'OR',\n   *       queries: ['motorcycle', 'archery']\n   *     }\n   *   ]\n   * })\n   *\n   * // Search for documents that contain (\"apple\" or \"pear\") but not \"juice\" and\n   * // not \"tree\"\n   * miniSearch.search({\n   *   combineWith: 'AND_NOT',\n   *   queries: [\n   *     {\n   *       combineWith: 'OR',\n   *       queries: ['apple', 'pear']\n   *     },\n   *     'juice',\n   *     'tree'\n   *   ]\n   * })\n   * ```\n   *\n   * Each node in the expression tree can be either a string, or an object that\n   * supports all {@link SearchOptions} fields, plus a `queries` array field for\n   * subqueries.\n   *\n   * Note that, while this can become complicated to do by hand for complex or\n   * deeply nested queries, it provides a formalized expression tree API for\n   * external libraries that implement a parser for custom query languages.\n   *\n   * @param query  Search query\n   * @param searchOptions  Search options. Each option, if not given, defaults to the corresponding value of `searchOptions` given to the constructor, or to the library default.\n   */\n  search (query: Query, searchOptions: SearchOptions = {}): SearchResult[] {\n    const { searchOptions: globalSearchOptions } = this._options\n    const searchOptionsWithDefaults: SearchOptionsWithDefaults = { ...globalSearchOptions, ...searchOptions }\n\n    const rawResults = this.executeQuery(query, searchOptions)\n    const results = []\n\n    for (const [docId, { score, terms, match }] of rawResults) {\n      // terms are the matched query terms, which will be returned to the user\n      // as queryTerms. The quality is calculated based on them, as opposed to\n      // the matched terms in the document (which can be different due to\n      // prefix and fuzzy match)\n      const quality = terms.length || 1\n\n      const result = {\n        id: this._documentIds.get(docId),\n        score: score * quality,\n        terms: Object.keys(match),\n        queryTerms: terms,\n        match\n      }\n\n      Object.assign(result, this._storedFields.get(docId))\n      if (searchOptionsWithDefaults.filter == null || searchOptionsWithDefaults.filter(result)) {\n        results.push(result)\n      }\n    }\n\n    // If it's a wildcard query, and no document boost is applied, skip sorting\n    // the results, as all results have the same score of 1\n    if (query === MiniSearch.wildcard && searchOptionsWithDefaults.boostDocument == null) {\n      return results\n    }\n\n    results.sort(byScore)\n    return results\n  }\n\n  /**\n   * Provide suggestions for the given search query\n   *\n   * The result is a list of suggested modified search queries, derived from the\n   * given search query, each with a relevance score, sorted by descending score.\n   *\n   * By default, it uses the same options used for search, except that by\n   * default it performs prefix search on the last term of the query, and\n   * combine terms with `'AND'` (requiring all query terms to match). Custom\n   * options can be passed as a second argument. Defaults can be changed upon\n   * calling the {@link MiniSearch} constructor, by passing a\n   * `autoSuggestOptions` option.\n   *\n   * ### Basic usage:\n   *\n   * ```javascript\n   * // Get suggestions for 'neuro':\n   * miniSearch.autoSuggest('neuro')\n   * // => [ { suggestion: 'neuromancer', terms: [ 'neuromancer' ], score: 0.46240 } ]\n   * ```\n   *\n   * ### Multiple words:\n   *\n   * ```javascript\n   * // Get suggestions for 'zen ar':\n   * miniSearch.autoSuggest('zen ar')\n   * // => [\n   * //  { suggestion: 'zen archery art', terms: [ 'zen', 'archery', 'art' ], score: 1.73332 },\n   * //  { suggestion: 'zen art', terms: [ 'zen', 'art' ], score: 1.21313 }\n   * // ]\n   * ```\n   *\n   * ### Fuzzy suggestions:\n   *\n   * ```javascript\n   * // Correct spelling mistakes using fuzzy search:\n   * miniSearch.autoSuggest('neromancer', { fuzzy: 0.2 })\n   * // => [ { suggestion: 'neuromancer', terms: [ 'neuromancer' ], score: 1.03998 } ]\n   * ```\n   *\n   * ### Filtering:\n   *\n   * ```javascript\n   * // Get suggestions for 'zen ar', but only within the 'fiction' category\n   * // (assuming that 'category' is a stored field):\n   * miniSearch.autoSuggest('zen ar', {\n   *   filter: (result) => result.category === 'fiction'\n   * })\n   * // => [\n   * //  { suggestion: 'zen archery art', terms: [ 'zen', 'archery', 'art' ], score: 1.73332 },\n   * //  { suggestion: 'zen art', terms: [ 'zen', 'art' ], score: 1.21313 }\n   * // ]\n   * ```\n   *\n   * @param queryString  Query string to be expanded into suggestions\n   * @param options  Search options. The supported options and default values\n   * are the same as for the {@link MiniSearch#search} method, except that by\n   * default prefix search is performed on the last term in the query, and terms\n   * are combined with `'AND'`.\n   * @return  A sorted array of suggestions sorted by relevance score.\n   */\n  autoSuggest (queryString: string, options: SearchOptions = {}): Suggestion[] {\n    options = { ...this._options.autoSuggestOptions, ...options }\n\n    const suggestions: Map<string, Omit<Suggestion, 'suggestion'> & { count: number }> = new Map()\n\n    for (const { score, terms } of this.search(queryString, options)) {\n      const phrase = terms.join(' ')\n      const suggestion = suggestions.get(phrase)\n      if (suggestion != null) {\n        suggestion.score += score\n        suggestion.count += 1\n      } else {\n        suggestions.set(phrase, { score, terms, count: 1 })\n      }\n    }\n\n    const results = []\n    for (const [suggestion, { score, terms, count }] of suggestions) {\n      results.push({ suggestion, terms, score: score / count })\n    }\n\n    results.sort(byScore)\n    return results\n  }\n\n  /**\n   * Total number of documents available to search\n   */\n  get documentCount (): number {\n    return this._documentCount\n  }\n\n  /**\n   * Number of terms in the index\n   */\n  get termCount (): number {\n    return this._index.size\n  }\n\n  /**\n   * Deserializes a JSON index (serialized with `JSON.stringify(miniSearch)`)\n   * and instantiates a MiniSearch instance. It should be given the same options\n   * originally used when serializing the index.\n   *\n   * ### Usage:\n   *\n   * ```javascript\n   * // If the index was serialized with:\n   * let miniSearch = new MiniSearch({ fields: ['title', 'text'] })\n   * miniSearch.addAll(documents)\n   *\n   * const json = JSON.stringify(miniSearch)\n   * // It can later be deserialized like this:\n   * miniSearch = MiniSearch.loadJSON(json, { fields: ['title', 'text'] })\n   * ```\n   *\n   * @param json  JSON-serialized index\n   * @param options  configuration options, same as the constructor\n   * @return An instance of MiniSearch deserialized from the given JSON.\n   */\n  static loadJSON<T = any> (json: string, options: Options<T>): MiniSearch<T> {\n    if (options == null) {\n      throw new Error('MiniSearch: loadJSON should be given the same options used when serializing the index')\n    }\n    return this.loadJS(JSON.parse(json), options)\n  }\n\n  /**\n   * Async equivalent of {@link MiniSearch.loadJSON}\n   *\n   * This function is an alternative to {@link MiniSearch.loadJSON} that returns\n   * a promise, and loads the index in batches, leaving pauses between them to avoid\n   * blocking the main thread. It tends to be slower than the synchronous\n   * version, but does not block the main thread, so it can be a better choice\n   * when deserializing very large indexes.\n   *\n   * @param json  JSON-serialized index\n   * @param options  configuration options, same as the constructor\n   * @return A Promise that will resolve to an instance of MiniSearch deserialized from the given JSON.\n   */\n  static async loadJSONAsync<T = any> (json: string, options: Options<T>): Promise<MiniSearch<T>> {\n    if (options == null) {\n      throw new Error('MiniSearch: loadJSON should be given the same options used when serializing the index')\n    }\n    return this.loadJSAsync(JSON.parse(json), options)\n  }\n\n  /**\n   * Returns the default value of an option. It will throw an error if no option\n   * with the given name exists.\n   *\n   * @param optionName  Name of the option\n   * @return The default value of the given option\n   *\n   * ### Usage:\n   *\n   * ```javascript\n   * // Get default tokenizer\n   * MiniSearch.getDefault('tokenize')\n   *\n   * // Get default term processor\n   * MiniSearch.getDefault('processTerm')\n   *\n   * // Unknown options will throw an error\n   * MiniSearch.getDefault('notExisting')\n   * // => throws 'MiniSearch: unknown option \"notExisting\"'\n   * ```\n   */\n  static getDefault (optionName: string): any {\n    if (defaultOptions.hasOwnProperty(optionName)) {\n      return getOwnProperty(defaultOptions, optionName)\n    } else {\n      throw new Error(`MiniSearch: unknown option \"${optionName}\"`)\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  static loadJS<T = any> (js: AsPlainObject, options: Options<T>): MiniSearch<T> {\n    const {\n      index,\n      documentIds,\n      fieldLength,\n      storedFields,\n      serializationVersion\n    } = js\n\n    const miniSearch = this.instantiateMiniSearch(js, options)\n\n    miniSearch._documentIds = objectToNumericMap(documentIds)\n    miniSearch._fieldLength = objectToNumericMap(fieldLength)\n    miniSearch._storedFields = objectToNumericMap(storedFields)\n\n    for (const [shortId, id] of miniSearch._documentIds) {\n      miniSearch._idToShortId.set(id, shortId)\n    }\n\n    for (const [term, data] of index) {\n      const dataMap = new Map() as FieldTermData\n\n      for (const fieldId of Object.keys(data)) {\n        let indexEntry = data[fieldId]\n\n        // Version 1 used to nest the index entry inside a field called ds\n        if (serializationVersion === 1) {\n          indexEntry = indexEntry.ds as unknown as SerializedIndexEntry\n        }\n\n        dataMap.set(parseInt(fieldId, 10), objectToNumericMap(indexEntry) as DocumentTermFreqs)\n      }\n\n      miniSearch._index.set(term, dataMap)\n    }\n\n    return miniSearch\n  }\n\n  /**\n   * @ignore\n   */\n  static async loadJSAsync<T = any> (js: AsPlainObject, options: Options<T>): Promise<MiniSearch<T>> {\n    const {\n      index,\n      documentIds,\n      fieldLength,\n      storedFields,\n      serializationVersion\n    } = js\n\n    const miniSearch = this.instantiateMiniSearch(js, options)\n\n    miniSearch._documentIds = await objectToNumericMapAsync(documentIds)\n    miniSearch._fieldLength = await objectToNumericMapAsync(fieldLength)\n    miniSearch._storedFields = await objectToNumericMapAsync(storedFields)\n\n    for (const [shortId, id] of miniSearch._documentIds) {\n      miniSearch._idToShortId.set(id, shortId)\n    }\n\n    let count = 0\n    for (const [term, data] of index) {\n      const dataMap = new Map() as FieldTermData\n\n      for (const fieldId of Object.keys(data)) {\n        let indexEntry = data[fieldId]\n\n        // Version 1 used to nest the index entry inside a field called ds\n        if (serializationVersion === 1) {\n          indexEntry = indexEntry.ds as unknown as SerializedIndexEntry\n        }\n\n        dataMap.set(parseInt(fieldId, 10), await objectToNumericMapAsync(indexEntry) as DocumentTermFreqs)\n      }\n\n      if (++count % 1000 === 0) await wait(0)\n      miniSearch._index.set(term, dataMap)\n    }\n\n    return miniSearch\n  }\n\n  /**\n   * @ignore\n   */\n  private static instantiateMiniSearch<T = any> (js: AsPlainObject, options: Options<T>): MiniSearch<T> {\n    const {\n      documentCount,\n      nextId,\n      fieldIds,\n      averageFieldLength,\n      dirtCount,\n      serializationVersion\n    } = js\n\n    if (serializationVersion !== 1 && serializationVersion !== 2) {\n      throw new Error('MiniSearch: cannot deserialize an index created with an incompatible version')\n    }\n\n    const miniSearch = new MiniSearch(options)\n\n    miniSearch._documentCount = documentCount\n    miniSearch._nextId = nextId\n    miniSearch._idToShortId = new Map<any, number>()\n    miniSearch._fieldIds = fieldIds\n    miniSearch._avgFieldLength = averageFieldLength\n    miniSearch._dirtCount = dirtCount || 0\n    miniSearch._index = new SearchableMap()\n\n    return miniSearch\n  }\n\n  /**\n   * @ignore\n   */\n  private executeQuery (query: Query, searchOptions: SearchOptions = {}): RawResult {\n    if (query === MiniSearch.wildcard) {\n      return this.executeWildcardQuery(searchOptions)\n    }\n\n    if (typeof query !== 'string') {\n      const options = { ...searchOptions, ...query, queries: undefined }\n      const results = query.queries.map((subquery) => this.executeQuery(subquery, options))\n      return this.combineResults(results, options.combineWith)\n    }\n\n    const { tokenize, processTerm, searchOptions: globalSearchOptions } = this._options\n    const options = { tokenize, processTerm, ...globalSearchOptions, ...searchOptions }\n    const { tokenize: searchTokenize, processTerm: searchProcessTerm } = options\n    const terms = searchTokenize(query)\n      .flatMap((term: string) => searchProcessTerm(term))\n      .filter((term) => !!term) as string[]\n    const queries: QuerySpec[] = terms.map(termToQuerySpec(options))\n    const results = queries.map(query => this.executeQuerySpec(query, options))\n\n    return this.combineResults(results, options.combineWith)\n  }\n\n  /**\n   * @ignore\n   */\n  private executeQuerySpec (query: QuerySpec, searchOptions: SearchOptions): RawResult {\n    const options: SearchOptionsWithDefaults = { ...this._options.searchOptions, ...searchOptions }\n\n    const boosts = (options.fields || this._options.fields).reduce((boosts, field) =>\n      ({ ...boosts, [field]: getOwnProperty(options.boost, field) || 1 }), {})\n\n    const {\n      boostDocument,\n      weights,\n      maxFuzzy,\n      bm25: bm25params\n    } = options\n\n    const { fuzzy: fuzzyWeight, prefix: prefixWeight } = { ...defaultSearchOptions.weights, ...weights }\n\n    const data = this._index.get(query.term)\n    const results = this.termResults(query.term, query.term, 1, query.termBoost, data, boosts, boostDocument, bm25params)\n\n    let prefixMatches\n    let fuzzyMatches\n\n    if (query.prefix) {\n      prefixMatches = this._index.atPrefix(query.term)\n    }\n\n    if (query.fuzzy) {\n      const fuzzy = (query.fuzzy === true) ? 0.2 : query.fuzzy\n      const maxDistance = fuzzy < 1 ? Math.min(maxFuzzy, Math.round(query.term.length * fuzzy)) : fuzzy\n      if (maxDistance) fuzzyMatches = this._index.fuzzyGet(query.term, maxDistance)\n    }\n\n    if (prefixMatches) {\n      for (const [term, data] of prefixMatches) {\n        const distance = term.length - query.term.length\n        if (!distance) { continue } // Skip exact match.\n\n        // Delete the term from fuzzy results (if present) if it is also a\n        // prefix result. This entry will always be scored as a prefix result.\n        fuzzyMatches?.delete(term)\n\n        // Weight gradually approaches 0 as distance goes to infinity, with the\n        // weight for the hypothetical distance 0 being equal to prefixWeight.\n        // The rate of change is much lower than that of fuzzy matches to\n        // account for the fact that prefix matches stay more relevant than\n        // fuzzy matches for longer distances.\n        const weight = prefixWeight * term.length / (term.length + 0.3 * distance)\n        this.termResults(query.term, term, weight, query.termBoost, data, boosts, boostDocument, bm25params, results)\n      }\n    }\n\n    if (fuzzyMatches) {\n      for (const term of fuzzyMatches.keys()) {\n        const [data, distance] = fuzzyMatches.get(term)!\n        if (!distance) { continue } // Skip exact match.\n\n        // Weight gradually approaches 0 as distance goes to infinity, with the\n        // weight for the hypothetical distance 0 being equal to fuzzyWeight.\n        const weight = fuzzyWeight * term.length / (term.length + distance)\n        this.termResults(query.term, term, weight, query.termBoost, data, boosts, boostDocument, bm25params, results)\n      }\n    }\n\n    return results\n  }\n\n  /**\n   * @ignore\n   */\n  private executeWildcardQuery (searchOptions: SearchOptions): RawResult {\n    const results = new Map() as RawResult\n    const options: SearchOptionsWithDefaults = { ...this._options.searchOptions, ...searchOptions }\n\n    for (const [shortId, id] of this._documentIds) {\n      const score = options.boostDocument ? options.boostDocument(id, '', this._storedFields.get(shortId)) : 1\n      results.set(shortId, {\n        score,\n        terms: [],\n        match: {}\n      })\n    }\n\n    return results\n  }\n\n  /**\n   * @ignore\n   */\n  private combineResults (results: RawResult[], combineWith: CombinationOperator = OR): RawResult {\n    if (results.length === 0) { return new Map() }\n    const operator = combineWith.toLowerCase()\n    const combinator = (combinators as Record<string, CombinatorFunction>)[operator]\n\n    if (!combinator) {\n      throw new Error(`Invalid combination operator: ${combineWith}`)\n    }\n\n    return results.reduce(combinator) || new Map()\n  }\n\n  /**\n   * Allows serialization of the index to JSON, to possibly store it and later\n   * deserialize it with {@link MiniSearch.loadJSON}.\n   *\n   * Normally one does not directly call this method, but rather call the\n   * standard JavaScript `JSON.stringify()` passing the {@link MiniSearch}\n   * instance, and JavaScript will internally call this method. Upon\n   * deserialization, one must pass to {@link MiniSearch.loadJSON} the same\n   * options used to create the original instance that was serialized.\n   *\n   * ### Usage:\n   *\n   * ```javascript\n   * // Serialize the index:\n   * let miniSearch = new MiniSearch({ fields: ['title', 'text'] })\n   * miniSearch.addAll(documents)\n   * const json = JSON.stringify(miniSearch)\n   *\n   * // Later, to deserialize it:\n   * miniSearch = MiniSearch.loadJSON(json, { fields: ['title', 'text'] })\n   * ```\n   *\n   * @return A plain-object serializable representation of the search index.\n   */\n  toJSON (): AsPlainObject {\n    const index: [string, { [key: string]: SerializedIndexEntry }][] = []\n\n    for (const [term, fieldIndex] of this._index) {\n      const data: { [key: string]: SerializedIndexEntry } = {}\n\n      for (const [fieldId, freqs] of fieldIndex) {\n        data[fieldId] = Object.fromEntries(freqs)\n      }\n\n      index.push([term, data])\n    }\n\n    return {\n      documentCount: this._documentCount,\n      nextId: this._nextId,\n      documentIds: Object.fromEntries(this._documentIds),\n      fieldIds: this._fieldIds,\n      fieldLength: Object.fromEntries(this._fieldLength),\n      averageFieldLength: this._avgFieldLength,\n      storedFields: Object.fromEntries(this._storedFields),\n      dirtCount: this._dirtCount,\n      index,\n      serializationVersion: 2\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  private termResults (\n    sourceTerm: string,\n    derivedTerm: string,\n    termWeight: number,\n    termBoost: number,\n    fieldTermData: FieldTermData | undefined,\n    fieldBoosts: { [field: string]: number },\n    boostDocumentFn: ((id: any, term: string, storedFields?: Record<string, unknown>) => number) | undefined,\n    bm25params: BM25Params,\n    results: RawResult = new Map()\n  ): RawResult {\n    if (fieldTermData == null) return results\n\n    for (const field of Object.keys(fieldBoosts)) {\n      const fieldBoost = fieldBoosts[field]\n      const fieldId = this._fieldIds[field]\n\n      const fieldTermFreqs = fieldTermData.get(fieldId)\n      if (fieldTermFreqs == null) continue\n\n      let matchingFields = fieldTermFreqs.size\n      const avgFieldLength = this._avgFieldLength[fieldId]\n\n      for (const docId of fieldTermFreqs.keys()) {\n        if (!this._documentIds.has(docId)) {\n          this.removeTerm(fieldId, docId, derivedTerm)\n          matchingFields -= 1\n          continue\n        }\n\n        const docBoost = boostDocumentFn ? boostDocumentFn(this._documentIds.get(docId), derivedTerm, this._storedFields.get(docId)) : 1\n        if (!docBoost) continue\n\n        const termFreq = fieldTermFreqs.get(docId)!\n        const fieldLength = this._fieldLength.get(docId)![fieldId]\n\n        // NOTE: The total number of fields is set to the number of documents\n        // `this._documentCount`. It could also make sense to use the number of\n        // documents where the current field is non-blank as a normalization\n        // factor. This will make a difference in scoring if the field is rarely\n        // present. This is currently not supported, and may require further\n        // analysis to see if it is a valid use case.\n        const rawScore = calcBM25Score(termFreq, matchingFields, this._documentCount, fieldLength, avgFieldLength, bm25params)\n        const weightedScore = termWeight * termBoost * fieldBoost * docBoost * rawScore\n\n        const result = results.get(docId)\n        if (result) {\n          result.score += weightedScore\n          assignUniqueTerm(result.terms, sourceTerm)\n          const match = getOwnProperty(result.match, derivedTerm)\n          if (match) {\n            match.push(field)\n          } else {\n            result.match[derivedTerm] = [field]\n          }\n        } else {\n          results.set(docId, {\n            score: weightedScore,\n            terms: [sourceTerm],\n            match: { [derivedTerm]: [field] }\n          })\n        }\n      }\n    }\n\n    return results\n  }\n\n  /**\n   * @ignore\n   */\n  private addTerm (fieldId: number, documentId: number, term: string): void {\n    const indexData = this._index.fetch(term, createMap)\n\n    let fieldIndex = indexData.get(fieldId)\n    if (fieldIndex == null) {\n      fieldIndex = new Map()\n      fieldIndex.set(documentId, 1)\n      indexData.set(fieldId, fieldIndex)\n    } else {\n      const docs = fieldIndex.get(documentId)\n      fieldIndex.set(documentId, (docs || 0) + 1)\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  private removeTerm (fieldId: number, documentId: number, term: string): void {\n    if (!this._index.has(term)) {\n      this.warnDocumentChanged(documentId, fieldId, term)\n      return\n    }\n\n    const indexData = this._index.fetch(term, createMap)\n\n    const fieldIndex = indexData.get(fieldId)\n    if (fieldIndex == null || fieldIndex.get(documentId) == null) {\n      this.warnDocumentChanged(documentId, fieldId, term)\n    } else if (fieldIndex.get(documentId)! <= 1) {\n      if (fieldIndex.size <= 1) {\n        indexData.delete(fieldId)\n      } else {\n        fieldIndex.delete(documentId)\n      }\n    } else {\n      fieldIndex.set(documentId, fieldIndex.get(documentId)! - 1)\n    }\n\n    if (this._index.get(term)!.size === 0) {\n      this._index.delete(term)\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  private warnDocumentChanged (shortDocumentId: number, fieldId: number, term: string): void {\n    for (const fieldName of Object.keys(this._fieldIds)) {\n      if (this._fieldIds[fieldName] === fieldId) {\n        this._options.logger('warn', `MiniSearch: document with ID ${this._documentIds.get(shortDocumentId)} has changed before removal: term \"${term}\" was not present in field \"${fieldName}\". Removing a document after it has changed can corrupt the index!`, 'version_conflict')\n        return\n      }\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  private addDocumentId (documentId: any): number {\n    const shortDocumentId = this._nextId\n    this._idToShortId.set(documentId, shortDocumentId)\n    this._documentIds.set(shortDocumentId, documentId)\n    this._documentCount += 1\n    this._nextId += 1\n    return shortDocumentId\n  }\n\n  /**\n   * @ignore\n   */\n  private addFields (fields: string[]): void {\n    for (let i = 0; i < fields.length; i++) {\n      this._fieldIds[fields[i]] = i\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  private addFieldLength (documentId: number, fieldId: number, count: number, length: number): void {\n    let fieldLengths = this._fieldLength.get(documentId)\n    if (fieldLengths == null) this._fieldLength.set(documentId, fieldLengths = [])\n    fieldLengths[fieldId] = length\n\n    const averageFieldLength = this._avgFieldLength[fieldId] || 0\n    const totalFieldLength = (averageFieldLength * count) + length\n    this._avgFieldLength[fieldId] = totalFieldLength / (count + 1)\n  }\n\n  /**\n   * @ignore\n   */\n  private removeFieldLength (documentId: number, fieldId: number, count: number, length: number): void {\n    if (count === 1) {\n      this._avgFieldLength[fieldId] = 0\n      return\n    }\n    const totalFieldLength = (this._avgFieldLength[fieldId] * count) - length\n    this._avgFieldLength[fieldId] = totalFieldLength / (count - 1)\n  }\n\n  /**\n   * @ignore\n   */\n  private saveStoredFields (documentId: number, doc: T): void {\n    const { storeFields, extractField } = this._options\n    if (storeFields == null || storeFields.length === 0) { return }\n\n    let documentFields = this._storedFields.get(documentId)\n    if (documentFields == null) this._storedFields.set(documentId, documentFields = {})\n\n    for (const fieldName of storeFields) {\n      const fieldValue = extractField(doc, fieldName)\n      if (fieldValue !== undefined) documentFields[fieldName] = fieldValue\n    }\n  }\n}\n\nconst getOwnProperty = (object: any, property: string) =>\n  Object.prototype.hasOwnProperty.call(object, property) ? object[property] : undefined\n\ntype CombinatorFunction = (a: RawResult, b: RawResult) => RawResult\n\nconst combinators: Record<LowercaseCombinationOperator, CombinatorFunction> = {\n  [OR]: (a: RawResult, b: RawResult) => {\n    for (const docId of b.keys()) {\n      const existing = a.get(docId)\n      if (existing == null) {\n        a.set(docId, b.get(docId)!)\n      } else {\n        const { score, terms, match } = b.get(docId)!\n        existing.score = existing.score + score\n        existing.match = Object.assign(existing.match, match)\n        assignUniqueTerms(existing.terms, terms)\n      }\n    }\n\n    return a\n  },\n  [AND]: (a: RawResult, b: RawResult) => {\n    const combined = new Map()\n\n    for (const docId of b.keys()) {\n      const existing = a.get(docId)\n      if (existing == null) continue\n\n      const { score, terms, match } = b.get(docId)!\n      assignUniqueTerms(existing.terms, terms)\n      combined.set(docId, {\n        score: existing.score + score,\n        terms: existing.terms,\n        match: Object.assign(existing.match, match)\n      })\n    }\n\n    return combined\n  },\n  [AND_NOT]: (a: RawResult, b: RawResult) => {\n    for (const docId of b.keys()) a.delete(docId)\n    return a\n  }\n}\n\n/**\n * Parameters of the BM25+ scoring algorithm. Customizing these is almost never\n * necessary, and finetuning them requires an understanding of the BM25 scoring\n * model.\n *\n * Some information about BM25 (and BM25+) can be found at these links:\n *\n *   - https://en.wikipedia.org/wiki/Okapi_BM25\n *   - https://opensourceconnections.com/blog/2015/10/16/bm25-the-next-generation-of-lucene-relevation/\n */\nexport type BM25Params = {\n  /** Term frequency saturation point.\n   *\n   * Recommended values are between `1.2` and `2`. Higher values increase the\n   * difference in score between documents with higher and lower term\n   * frequencies. Setting this to `0` or a negative value is invalid. Defaults\n   * to `1.2`\n   */\n  k: number,\n\n  /**\n   * Length normalization impact.\n   *\n   * Recommended values are around `0.75`. Higher values increase the weight\n   * that field length has on scoring. Setting this to `0` (not recommended)\n   * means that the field length has no effect on scoring. Negative values are\n   * invalid. Defaults to `0.7`.\n   */\n  b: number,\n\n  /**\n   * BM25+ frequency normalization lower bound (usually called δ).\n   *\n   * Recommended values are between `0.5` and `1`. Increasing this parameter\n   * increases the minimum relevance of one occurrence of a search term\n   * regardless of its (possibly very long) field length. Negative values are\n   * invalid. Defaults to `0.5`.\n   */\n  d: number\n}\n\nconst defaultBM25params: BM25Params = { k: 1.2, b: 0.7, d: 0.5 }\n\nconst calcBM25Score = (\n  termFreq: number,\n  matchingCount: number,\n  totalCount: number,\n  fieldLength: number,\n  avgFieldLength: number,\n  bm25params: BM25Params\n): number => {\n  const { k, b, d } = bm25params\n  const invDocFreq = Math.log(1 + (totalCount - matchingCount + 0.5) / (matchingCount + 0.5))\n  return invDocFreq * (d + termFreq * (k + 1) / (termFreq + k * (1 - b + b * fieldLength / avgFieldLength)))\n}\n\nconst termToQuerySpec = (options: SearchOptions) => (term: string, i: number, terms: string[]): QuerySpec => {\n  const fuzzy = (typeof options.fuzzy === 'function')\n    ? options.fuzzy(term, i, terms)\n    : (options.fuzzy || false)\n  const prefix = (typeof options.prefix === 'function')\n    ? options.prefix(term, i, terms)\n    : (options.prefix === true)\n  const termBoost = (typeof options.boostTerm === 'function')\n    ? options.boostTerm(term, i, terms)\n    : 1\n  return { term, fuzzy, prefix, termBoost }\n}\n\nconst defaultOptions = {\n  idField: 'id',\n  extractField: (document: any, fieldName: string) => document[fieldName],\n  tokenize: (text: string) => text.split(SPACE_OR_PUNCTUATION),\n  processTerm: (term: string) => term.toLowerCase(),\n  fields: undefined,\n  searchOptions: undefined,\n  storeFields: [],\n  logger: (level: LogLevel, message: string): void => {\n    if (typeof console?.[level] === 'function') console[level](message)\n  },\n  autoVacuum: true\n}\n\nconst defaultSearchOptions = {\n  combineWith: OR,\n  prefix: false,\n  fuzzy: false,\n  maxFuzzy: 6,\n  boost: {},\n  weights: { fuzzy: 0.45, prefix: 0.375 },\n  bm25: defaultBM25params\n}\n\nconst defaultAutoSuggestOptions = {\n  combineWith: AND,\n  prefix: (term: string, i: number, terms: string[]): boolean =>\n    i === terms.length - 1\n}\n\nconst defaultVacuumOptions = { batchSize: 1000, batchWait: 10 }\nconst defaultVacuumConditions = { minDirtFactor: 0.1, minDirtCount: 20 }\n\nconst defaultAutoVacuumOptions = { ...defaultVacuumOptions, ...defaultVacuumConditions }\n\nconst assignUniqueTerm = (target: string[], term: string): void => {\n  // Avoid adding duplicate terms.\n  if (!target.includes(term)) target.push(term)\n}\n\nconst assignUniqueTerms = (target: string[], source: readonly string[]): void => {\n  for (const term of source) {\n    // Avoid adding duplicate terms.\n    if (!target.includes(term)) target.push(term)\n  }\n}\n\ntype Scored = { score: number }\nconst byScore = ({ score: a }: Scored, { score: b }: Scored) => b - a\n\nconst createMap = () => new Map()\n\ninterface SerializedIndexEntry {\n  [key: string]: number\n}\n\nconst objectToNumericMap = <T>(object: { [key: string]: T }): Map<number, T> => {\n  const map = new Map()\n\n  for (const key of Object.keys(object)) {\n    map.set(parseInt(key, 10), object[key])\n  }\n\n  return map\n}\n\nconst objectToNumericMapAsync = async <T>(object: { [key: string]: T }): Promise<Map<number, T>> => {\n  const map = new Map()\n\n  let count = 0\n  for (const key of Object.keys(object)) {\n    map.set(parseInt(key, 10), object[key])\n    if (++count % 1000 === 0) {\n      await wait(0)\n    }\n  }\n\n  return map\n}\n\nconst wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))\n\n// This regular expression matches any Unicode space, newline, or punctuation\n// character\nconst SPACE_OR_PUNCTUATION = /[\\n\\r\\p{Z}\\p{P}]+/u\n"], "mappings": ";AAGA,IAAM,UAAU;AAGhB,IAAM,OAAO;AAGb,IAAM,SAAS;AAGf,IAAM,OAAO;AAwBb,IAAM,eAAN,MAAkB;EAKhB,YAAa,KAAqB,MAAO;AACvC,UAAM,OAAO,IAAI;AACjB,UAAM,OAAO,MAAM,KAAK,KAAK,KAAI,CAAE;AACnC,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,QAAQ,KAAK,SAAS,IAAI,CAAC,EAAE,MAAM,KAAI,CAAE,IAAI,CAAA;;EAGpD,OAAI;AACF,UAAM,QAAQ,KAAK,KAAI;AACvB,SAAK,UAAS;AACd,WAAO;;EAGT,OAAI;AACF,QAAI,KAAK,MAAM,WAAW,GAAG;AAAE,aAAO,EAAE,MAAM,MAAM,OAAO,OAAS;;AACpE,UAAM,EAAE,MAAM,KAAI,IAAKA,OAAK,KAAK,KAAK;AACtC,QAAIA,OAAK,IAAI,MAAM,MAAM;AAAE,aAAO,EAAE,MAAM,OAAO,OAAO,KAAK,OAAM,EAAE;;AAErE,UAAM,QAAQ,KAAK,IAAIA,OAAK,IAAI,CAAE;AAClC,SAAK,MAAM,KAAK,EAAE,MAAM,OAAO,MAAM,MAAM,KAAK,MAAM,KAAI,CAAE,EAAC,CAAE;AAC/D,WAAO,KAAK,KAAI;;EAGlB,YAAS;AACP,QAAI,KAAK,MAAM,WAAW,GAAG;AAAE;;AAC/B,UAAM,OAAOA,OAAK,KAAK,KAAK,EAAG;AAC/B,SAAK,IAAG;AACR,QAAI,KAAK,SAAS,GAAG;AAAE;;AACvB,SAAK,MAAM,IAAG;AACd,SAAK,UAAS;;EAGhB,MAAG;AACD,WAAO,KAAK,IAAI,UAAU,KAAK,MAC5B,IAAI,CAAC,EAAE,KAAI,MAAOA,OAAK,IAAI,CAAC,EAC5B,OAAO,SAAO,QAAQ,IAAI,EAC1B,KAAK,EAAE;;EAGZ,QAAK;AACH,WAAOA,OAAK,KAAK,KAAK,EAAG,KAAK,IAAI,IAAI;;EAGxC,SAAM;AACJ,YAAQ,KAAK,OAAK;MAChB,KAAK;AAAQ,eAAO,KAAK,MAAK;MAC9B,KAAK;AAAM,eAAO,KAAK,IAAG;MAC1B;AAAS,eAAO,CAAC,KAAK,IAAG,GAAI,KAAK,MAAK,CAAE;;;EAI7C,CAAC,OAAO,QAAQ,IAAC;AACf,WAAO;;AAEV;AAED,IAAMA,SAAO,CAAI,UAA6B;AAC5C,SAAO,MAAM,MAAM,SAAS,CAAC;AAC/B;ACzFO,IAAM,cAAc,CAAU,MAAoB,OAAe,gBAAwC;AAC9G,QAAM,UAA2B,oBAAI,IAAG;AACxC,MAAI,UAAU;AAAW,WAAO;AAGhC,QAAM,IAAI,MAAM,SAAS;AAGzB,QAAM,IAAI,IAAI;AAGd,QAAM,SAAS,IAAI,WAAW,IAAI,CAAC,EAAE,KAAK,cAAc,CAAC;AACzD,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AAAG,WAAO,CAAC,IAAI;AACxC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AAAG,WAAO,IAAI,CAAC,IAAI;AAE5C,UACE,MACA,OACA,aACA,SACA,QACA,GACA,GACA,EAAE;AAGJ,SAAO;AACT;AAiBA,IAAM,UAAU,CACd,MACA,OACA,aACA,SACA,QACA,GACA,GACA,WACQ;AACR,QAAM,SAAS,IAAI;AAEnB,MAAK,YAAW,OAAO,KAAK,KAAI,GAAI;AAClC,QAAI,QAAQ,MAAM;AAGhB,YAAM,WAAW,OAAO,SAAS,CAAC;AAClC,UAAI,YAAY,aAAa;AAC3B,gBAAQ,IAAI,QAAQ,CAAC,KAAK,IAAI,GAAG,GAAI,QAAQ,CAAC;;WAE3C;AAIL,UAAI,IAAI;AACR,eAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,EAAE,KAAK,EAAE,GAAG;AAC9C,cAAM,OAAO,IAAI,GAAG;AACpB,cAAM,gBAAgB,IAAI;AAC1B,cAAM,gBAAgB,gBAAgB;AAItC,YAAI,cAAc,OAAO,aAAa;AAEtC,cAAM,OAAO,KAAK,IAAI,GAAG,IAAI,cAAc,CAAC;AAC5C,cAAM,OAAO,KAAK,IAAI,IAAI,GAAG,IAAI,WAAW;AAG5C,iBAAS,IAAI,MAAM,IAAI,MAAM,EAAE,GAAG;AAChC,gBAAM,YAAY,SAAS,MAAM,CAAC;AAKlC,gBAAM,MAAM,OAAO,gBAAgB,CAAC,IAAI,CAAC;AACzC,gBAAM,MAAM,OAAO,gBAAgB,IAAI,CAAC,IAAI;AAC5C,gBAAM,MAAM,OAAO,gBAAgB,CAAC,IAAI;AAExC,gBAAM,OAAO,OAAO,gBAAgB,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG;AAEnE,cAAI,OAAO;AAAa,0BAAc;;AAKxC,YAAI,cAAc,aAAa;AAC7B,mBAAS;;;AAIb,cACE,KAAK,IAAI,GAAG,GACZ,OACA,aACA,SACA,QACA,GACA,GACA,SAAS,GAAG;;;AAIpB;AC5Gc,IAAO,gBAAP,MAAO,eAAa;;;;;;;;;;EAsBhC,YAAa,OAAqB,oBAAI,IAAG,GAAI,SAAS,IAAE;AAXhD,SAAK,QAAuB;AAYlC,SAAK,QAAQ;AACb,SAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgCjB,SAAU,QAAc;AACtB,QAAI,CAAC,OAAO,WAAW,KAAK,OAAO,GAAG;AAAE,YAAM,IAAI,MAAM,mBAAmB;;AAE3E,UAAM,CAAC,MAAM,IAAI,IAAI,UAAU,KAAK,OAAO,OAAO,MAAM,KAAK,QAAQ,MAAM,CAAC;AAE5E,QAAI,SAAS,QAAW;AACtB,YAAM,CAAC,YAAY,GAAG,IAAI,KAAK,IAAI;AAEnC,iBAAW,KAAK,WAAY,KAAI,GAAI;AAClC,YAAI,MAAM,QAAQ,EAAE,WAAW,GAAG,GAAG;AACnC,gBAAMC,QAAO,oBAAI,IAAG;AACpB,UAAAA,MAAK,IAAI,EAAE,MAAM,IAAI,MAAM,GAAG,WAAY,IAAI,CAAC,CAAE;AACjD,iBAAO,IAAI,eAAcA,OAAM,MAAM;;;;AAK3C,WAAO,IAAI,eAAiB,MAAM,MAAM;;;;;EAM1C,QAAK;AACH,SAAK,QAAQ;AACb,SAAK,MAAM,MAAK;;;;;;EAOlB,OAAQ,KAAW;AACjB,SAAK,QAAQ;AACb,WAAO,OAAO,KAAK,OAAO,GAAG;;;;;;EAO/B,UAAO;AACL,WAAO,IAAI,aAAa,MAAM,OAAO;;;;;;EAOvC,QAAS,IAAuD;AAC9D,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,SAAG,KAAK,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgCvB,SAAU,KAAa,iBAAuB;AAC5C,WAAO,YAAe,KAAK,OAAO,KAAK,eAAe;;;;;;;;EASxD,IAAK,KAAW;AACd,UAAM,OAAO,OAAU,KAAK,OAAO,GAAG;AACtC,WAAO,SAAS,SAAY,KAAK,IAAI,IAAI,IAAI;;;;;;;EAQ/C,IAAK,KAAW;AACd,UAAM,OAAO,OAAO,KAAK,OAAO,GAAG;AACnC,WAAO,SAAS,UAAa,KAAK,IAAI,IAAI;;;;;;EAO5C,OAAI;AACF,WAAO,IAAI,aAAa,MAAM,IAAI;;;;;;;;EASpC,IAAK,KAAa,OAAQ;AACxB,QAAI,OAAO,QAAQ,UAAU;AAAE,YAAM,IAAI,MAAM,sBAAsB;;AACrE,SAAK,QAAQ;AACb,UAAM,OAAO,WAAW,KAAK,OAAO,GAAG;AACvC,SAAK,IAAI,MAAM,KAAK;AACpB,WAAO;;;;;EAMT,IAAI,OAAI;AACN,QAAI,KAAK,OAAO;AAAE,aAAO,KAAK;;AAE9B,SAAK,QAAQ;AAEb,UAAM,OAAO,KAAK,QAAO;AACzB,WAAO,CAAC,KAAK,KAAI,EAAG;AAAM,WAAK,SAAU;AAEzC,WAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;EAuBd,OAAQ,KAAa,IAA+B;AAClD,QAAI,OAAO,QAAQ,UAAU;AAAE,YAAM,IAAI,MAAM,sBAAsB;;AACrE,SAAK,QAAQ;AACb,UAAM,OAAO,WAAW,KAAK,OAAO,GAAG;AACvC,SAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AACjC,WAAO;;;;;;;;;;;;;;;;;;EAmBT,MAAO,KAAa,SAAgB;AAClC,QAAI,OAAO,QAAQ,UAAU;AAAE,YAAM,IAAI,MAAM,sBAAsB;;AACrE,SAAK,QAAQ;AACb,UAAM,OAAO,WAAW,KAAK,OAAO,GAAG;AAEvC,QAAI,QAAQ,KAAK,IAAI,IAAI;AACzB,QAAI,UAAU,QAAW;AACvB,WAAK,IAAI,MAAM,QAAQ,QAAO,CAAE;;AAGlC,WAAO;;;;;;EAOT,SAAM;AACJ,WAAO,IAAI,aAAa,MAAM,MAAM;;;;;EAMtC,CAAC,OAAO,QAAQ,IAAC;AACf,WAAO,KAAK,QAAO;;;;;;;;EASrB,OAAO,KAAe,SAAwC;AAC5D,UAAM,OAAO,IAAI,eAAa;AAC9B,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,WAAK,IAAI,KAAK,KAAK;;AAErB,WAAO;;;;;;;;EAST,OAAO,WAAqB,QAA4B;AACtD,WAAO,eAAc,KAAQ,OAAO,QAAQ,MAAM,CAAC;;AAEtD;AAED,IAAM,YAAY,CAAU,MAAgC,KAAa,OAAgB,CAAA,MAA2C;AAClI,MAAI,IAAI,WAAW,KAAK,QAAQ,MAAM;AAAE,WAAO,CAAC,MAAM,IAAI;;AAE1D,aAAW,KAAK,KAAK,KAAI,GAAI;AAC3B,QAAI,MAAM,QAAQ,IAAI,WAAW,CAAC,GAAG;AACnC,WAAK,KAAK,CAAC,MAAM,CAAC,CAAC;AACnB,aAAO,UAAU,KAAK,IAAI,CAAC,GAAI,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI;;;AAI5D,OAAK,KAAK,CAAC,MAAM,GAAG,CAAC;AACrB,SAAO,UAAU,QAAW,IAAI,IAAI;AACtC;AAEA,IAAM,SAAS,CAAU,MAAoB,QAAyC;AACpF,MAAI,IAAI,WAAW,KAAK,QAAQ,MAAM;AAAE,WAAO;;AAE/C,aAAW,KAAK,KAAK,KAAI,GAAI;AAC3B,QAAI,MAAM,QAAQ,IAAI,WAAW,CAAC,GAAG;AACnC,aAAO,OAAO,KAAK,IAAI,CAAC,GAAI,IAAI,MAAM,EAAE,MAAM,CAAC;;;AAGrD;AAKA,IAAM,aAAa,CAAU,MAAoB,QAA6B;AAC5E,QAAM,YAAY,IAAI;AAEtB,QAAO,UAAS,MAAM,GAAG,QAAQ,MAAM,aAAY;AACjD,eAAW,KAAK,KAAK,KAAI,GAAI;AAE3B,UAAI,MAAM,QAAQ,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG;AACnC,cAAM,MAAM,KAAK,IAAI,YAAY,KAAK,EAAE,MAAM;AAG9C,YAAI,SAAS;AACb,eAAO,SAAS,OAAO,IAAI,MAAM,MAAM,MAAM,EAAE,MAAM;AAAG,YAAE;AAE1D,cAAMC,SAAQ,KAAK,IAAI,CAAC;AACxB,YAAI,WAAW,EAAE,QAAQ;AAEvB,iBAAOA;eACF;AAGL,gBAAM,eAAe,oBAAI,IAAG;AAC5B,uBAAa,IAAI,EAAE,MAAM,MAAM,GAAGA,MAAK;AACvC,eAAK,IAAI,IAAI,MAAM,KAAK,MAAM,MAAM,GAAG,YAAY;AACnD,eAAK,OAAO,CAAC;AACb,iBAAO;;AAGT,eAAO;AACP,iBAAS;;;AAKb,UAAM,QAAQ,oBAAI,IAAG;AACrB,SAAK,IAAI,IAAI,MAAM,GAAG,GAAG,KAAK;AAC9B,WAAO;;AAGT,SAAO;AACT;AAEA,IAAM,SAAS,CAAU,MAAoB,QAAqB;AAChE,QAAM,CAAC,MAAM,IAAI,IAAI,UAAU,MAAM,GAAG;AACxC,MAAI,SAAS,QAAW;AAAE;;AAC1B,OAAK,OAAO,IAAI;AAEhB,MAAI,KAAK,SAAS,GAAG;AACnB,YAAQ,IAAI;aACH,KAAK,SAAS,GAAG;AAC1B,UAAM,CAACC,MAAK,KAAK,IAAI,KAAK,QAAO,EAAG,KAAI,EAAG;AAC3C,UAAM,MAAMA,MAAe,KAAqB;;AAEpD;AAEA,IAAM,UAAU,CAAU,SAAuB;AAC/C,MAAI,KAAK,WAAW,GAAG;AAAE;;AAEzB,QAAM,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI;AAC7B,OAAM,OAAO,GAAG;AAEhB,MAAI,KAAM,SAAS,GAAG;AACpB,YAAQ,KAAK,MAAM,GAAG,EAAE,CAAC;aAChB,KAAM,SAAS,GAAG;AAC3B,UAAM,CAACA,MAAK,KAAK,IAAI,KAAM,QAAO,EAAG,KAAI,EAAG;AAC5C,QAAIA,SAAQ,MAAM;AAChB,YAAM,KAAK,MAAM,GAAG,EAAE,GAAGA,MAAe,KAAqB;;;AAGnE;AAEA,IAAM,QAAQ,CAAU,MAAe,KAAa,UAA6B;AAC/E,MAAI,KAAK,WAAW,GAAG;AAAE;;AAEzB,QAAM,CAAC,MAAM,OAAO,IAAI,KAAK,IAAI;AACjC,OAAM,IAAI,UAAU,KAAK,KAAK;AAC9B,OAAM,OAAO,OAAO;AACtB;AAEA,IAAM,OAAO,CAAU,UAAiB;AACtC,SAAO,MAAM,MAAM,SAAS,CAAC;AAC/B;AClaA,IAAM,KAAmC;AACzC,IAAM,MAAoC;AAC1C,IAAM,UAAwC;AA0jB9C,IAAqB,aAArB,MAAqB,YAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmF7B,YAAa,SAAmB;AAC9B,SAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAU,MAAM;AAC3B,YAAM,IAAI,MAAM,8CAA8C;;AAGhE,UAAM,aAAc,QAAQ,cAAc,QAAQ,QAAQ,eAAe,OAAQ,2BAA2B,QAAQ;AAEpH,SAAK,WAAW;MACd,GAAG;MACH,GAAG;MACH;MACA,eAAe,EAAE,GAAG,sBAAsB,GAAI,QAAQ,iBAAiB,CAAA,EAAG;MAC1E,oBAAoB,EAAE,GAAG,2BAA2B,GAAI,QAAQ,sBAAsB,CAAA,EAAG;;AAG3F,SAAK,SAAS,IAAI,cAAa;AAE/B,SAAK,iBAAiB;AAEtB,SAAK,eAAe,oBAAI,IAAG;AAE3B,SAAK,eAAe,oBAAI,IAAG;AAM3B,SAAK,YAAY,CAAA;AAEjB,SAAK,eAAe,oBAAI,IAAG;AAE3B,SAAK,kBAAkB,CAAA;AAEvB,SAAK,UAAU;AAEf,SAAK,gBAAgB,oBAAI,IAAG;AAE5B,SAAK,aAAa;AAElB,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB;AACvB,SAAK,4BAA4B;AAEjC,SAAK,UAAU,KAAK,SAAS,MAAM;;;;;;;EAQrC,IAAK,UAAW;AACd,UAAM,EAAE,cAAc,UAAU,aAAa,QAAQ,QAAO,IAAK,KAAK;AACtE,UAAM,KAAK,aAAa,UAAU,OAAO;AACzC,QAAI,MAAM,MAAM;AACd,YAAM,IAAI,MAAM,gDAAgD,OAAO,GAAG;;AAG5E,QAAI,KAAK,aAAa,IAAI,EAAE,GAAG;AAC7B,YAAM,IAAI,MAAM,4BAA4B,EAAE,EAAE;;AAGlD,UAAM,kBAAkB,KAAK,cAAc,EAAE;AAC7C,SAAK,iBAAiB,iBAAiB,QAAQ;AAE/C,eAAW,SAAS,QAAQ;AAC1B,YAAM,aAAa,aAAa,UAAU,KAAK;AAC/C,UAAI,cAAc;AAAM;AAExB,YAAM,SAAS,SAAS,WAAW,SAAQ,GAAI,KAAK;AACpD,YAAM,UAAU,KAAK,UAAU,KAAK;AAEpC,YAAM,cAAc,IAAI,IAAI,MAAM,EAAE;AACpC,WAAK,eAAe,iBAAiB,SAAS,KAAK,iBAAiB,GAAG,WAAW;AAElF,iBAAW,QAAQ,QAAQ;AACzB,cAAM,gBAAgB,YAAY,MAAM,KAAK;AAC7C,YAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,qBAAW,KAAK,eAAe;AAC7B,iBAAK,QAAQ,SAAS,iBAAiB,CAAC;;mBAEjC,eAAe;AACxB,eAAK,QAAQ,SAAS,iBAAiB,aAAa;;;;;;;;;;EAW5D,OAAQ,WAAuB;AAC7B,eAAW,YAAY;AAAW,WAAK,IAAI,QAAQ;;;;;;;;;;;;;EAcrD,YAAa,WAAyB,UAAkC,CAAA,GAAE;AACxE,UAAM,EAAE,YAAY,GAAE,IAAK;AAC3B,UAAM,MAA8C,EAAE,OAAO,CAAA,GAAI,SAAS,QAAQ,QAAO,EAAE;AAE3F,UAAM,EAAE,OAAO,QAAO,IAAK,UAAU,OAAO,CAAC,EAAE,OAAAC,QAAO,SAAAC,SAAO,GAAI,UAAa,MAAa;AACzF,MAAAD,OAAM,KAAK,QAAQ;AACnB,WAAK,IAAI,KAAK,cAAc,GAAG;AAC7B,eAAO;UACL,OAAO,CAAA;UACP,SAASC,SACN,KAAK,MAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,CAAC,CAAC,CAAC,EACzD,KAAK,MAAM,KAAK,OAAOD,MAAK,CAAC;;aAE7B;AACL,eAAO,EAAE,OAAAA,QAAO,SAAAC,SAAO;;OAExB,GAAG;AAEN,WAAO,QAAQ,KAAK,MAAM,KAAK,OAAO,KAAK,CAAC;;;;;;;;;;;;;;;;EAiB9C,OAAQ,UAAW;AACjB,UAAM,EAAE,UAAU,aAAa,cAAc,QAAQ,QAAO,IAAK,KAAK;AACtE,UAAM,KAAK,aAAa,UAAU,OAAO;AAEzC,QAAI,MAAM,MAAM;AACd,YAAM,IAAI,MAAM,gDAAgD,OAAO,GAAG;;AAG5E,UAAM,UAAU,KAAK,aAAa,IAAI,EAAE;AAExC,QAAI,WAAW,MAAM;AACnB,YAAM,IAAI,MAAM,8CAA8C,EAAE,0BAA0B;;AAG5F,eAAW,SAAS,QAAQ;AAC1B,YAAM,aAAa,aAAa,UAAU,KAAK;AAC/C,UAAI,cAAc;AAAM;AAExB,YAAM,SAAS,SAAS,WAAW,SAAQ,GAAI,KAAK;AACpD,YAAM,UAAU,KAAK,UAAU,KAAK;AAEpC,YAAM,cAAc,IAAI,IAAI,MAAM,EAAE;AACpC,WAAK,kBAAkB,SAAS,SAAS,KAAK,gBAAgB,WAAW;AAEzE,iBAAW,QAAQ,QAAQ;AACzB,cAAM,gBAAgB,YAAY,MAAM,KAAK;AAC7C,YAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,qBAAW,KAAK,eAAe;AAC7B,iBAAK,WAAW,SAAS,SAAS,CAAC;;mBAE5B,eAAe;AACxB,eAAK,WAAW,SAAS,SAAS,aAAa;;;;AAKrD,SAAK,cAAc,OAAO,OAAO;AACjC,SAAK,aAAa,OAAO,OAAO;AAChC,SAAK,aAAa,OAAO,EAAE;AAC3B,SAAK,aAAa,OAAO,OAAO;AAChC,SAAK,kBAAkB;;;;;;;;;;;EAYzB,UAAW,WAAwB;AACjC,QAAI,WAAW;AACb,iBAAW,YAAY;AAAW,aAAK,OAAO,QAAQ;eAC7C,UAAU,SAAS,GAAG;AAC/B,YAAM,IAAI,MAAM,8EAA8E;WACzF;AACL,WAAK,SAAS,IAAI,cAAa;AAC/B,WAAK,iBAAiB;AACtB,WAAK,eAAe,oBAAI,IAAG;AAC3B,WAAK,eAAe,oBAAI,IAAG;AAC3B,WAAK,eAAe,oBAAI,IAAG;AAC3B,WAAK,kBAAkB,CAAA;AACvB,WAAK,gBAAgB,oBAAI,IAAG;AAC5B,WAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgDnB,QAAS,IAAO;AACd,UAAM,UAAU,KAAK,aAAa,IAAI,EAAE;AAExC,QAAI,WAAW,MAAM;AACnB,YAAM,IAAI,MAAM,+CAA+C,EAAE,0BAA0B;;AAG7F,SAAK,aAAa,OAAO,EAAE;AAC3B,SAAK,aAAa,OAAO,OAAO;AAChC,SAAK,cAAc,OAAO,OAAO;AAEhC,KAAC,KAAK,aAAa,IAAI,OAAO,KAAK,CAAA,GAAI,QAAQ,CAAC,aAAa,YAAW;AACvE,WAAK,kBAAkB,SAAS,SAAS,KAAK,gBAAgB,WAAW;IAC3E,CAAC;AAED,SAAK,aAAa,OAAO,OAAO;AAEhC,SAAK,kBAAkB;AACvB,SAAK,cAAc;AAEnB,SAAK,gBAAe;;EAGd,kBAAe;AACrB,QAAI,KAAK,SAAS,eAAe,OAAO;AAAE;;AAE1C,UAAM,EAAE,eAAe,cAAc,WAAW,UAAS,IAAK,KAAK,SAAS;AAC5E,SAAK,kBAAkB,EAAE,WAAW,UAAS,GAAI,EAAE,cAAc,cAAa,CAAE;;;;;;;;;;;;;;EAelF,WAAY,KAAmB;AAC7B,UAAM,aAAa,KAAK,SAAS;AAEjC,QAAI;AACF,WAAK,SAAS,aAAa;AAE3B,iBAAW,MAAM,KAAK;AACpB,aAAK,QAAQ,EAAE;;;AAGjB,WAAK,SAAS,aAAa;;AAG7B,SAAK,gBAAe;;;;;;;;;;;;;;;;;EAkBtB,QAAS,iBAAkB;AACzB,UAAM,EAAE,SAAS,aAAY,IAAK,KAAK;AACvC,UAAM,KAAK,aAAa,iBAAiB,OAAO;AAEhD,SAAK,QAAQ,EAAE;AACf,SAAK,IAAI,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0C1B,OAAQ,UAAyB,CAAA,GAAE;AACjC,WAAO,KAAK,kBAAkB,OAAO;;EAG/B,kBAAmB,SAAwB,YAA6B;AAK9E,QAAI,KAAK,gBAAgB;AACvB,WAAK,4BAA4B,KAAK,6BAA6B;AACnE,UAAI,KAAK,mBAAmB,MAAM;AAAE,eAAO,KAAK;;AAEhD,WAAK,kBAAkB,KAAK,eAAe,KAAK,MAAK;AACnD,cAAMC,cAAa,KAAK;AACxB,aAAK,4BAA4B;AACjC,eAAO,KAAK,iBAAiB,SAASA,WAAU;MAClD,CAAC;AACD,aAAO,KAAK;;AAGd,QAAI,KAAK,oBAAoB,UAAU,MAAM,OAAO;AAAE,aAAO,QAAQ,QAAO;;AAE5E,SAAK,iBAAiB,KAAK,iBAAiB,OAAO;AACnD,WAAO,KAAK;;EAGN,MAAM,iBAAkB,SAAwB,YAA6B;AACnF,UAAM,mBAAmB,KAAK;AAE9B,QAAI,KAAK,oBAAoB,UAAU,GAAG;AACxC,YAAM,YAAY,QAAQ,aAAa,qBAAqB;AAC5D,YAAM,YAAY,QAAQ,aAAa,qBAAqB;AAC5D,UAAI,IAAI;AAER,iBAAW,CAAC,MAAM,UAAU,KAAK,KAAK,QAAQ;AAC5C,mBAAW,CAAC,SAAS,UAAU,KAAK,YAAY;AAC9C,qBAAW,CAAC,OAAO,KAAK,YAAY;AAClC,gBAAI,KAAK,aAAa,IAAI,OAAO,GAAG;AAAE;;AAEtC,gBAAI,WAAW,QAAQ,GAAG;AACxB,yBAAW,OAAO,OAAO;mBACpB;AACL,yBAAW,OAAO,OAAO;;;;AAK/B,YAAI,KAAK,OAAO,IAAI,IAAI,EAAG,SAAS,GAAG;AACrC,eAAK,OAAO,OAAO,IAAI;;AAGzB,YAAI,IAAI,cAAc,GAAG;AACvB,gBAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,SAAS,CAAC;;AAG/D,aAAK;;AAGP,WAAK,cAAc;;AAIrB,UAAM;AAEN,SAAK,iBAAiB,KAAK;AAC3B,SAAK,kBAAkB;;EAGjB,oBAAqB,YAA6B;AACxD,QAAI,cAAc,MAAM;AAAE,aAAO;;AAEjC,QAAI,EAAE,cAAc,cAAa,IAAK;AACtC,mBAAe,gBAAgB,yBAAyB;AACxD,oBAAgB,iBAAiB,yBAAyB;AAE1D,WAAO,KAAK,aAAa,gBAAgB,KAAK,cAAc;;;;;EAM9D,IAAI,cAAW;AACb,WAAO,KAAK,kBAAkB;;;;;EAMhC,IAAI,YAAS;AACX,WAAO,KAAK;;;;;;;;;EAUd,IAAI,aAAU;AACZ,WAAO,KAAK,cAAc,IAAI,KAAK,iBAAiB,KAAK;;;;;;;;EAS3D,IAAK,IAAO;AACV,WAAO,KAAK,aAAa,IAAI,EAAE;;;;;;;;;EAUjC,gBAAiB,IAAO;AACtB,UAAM,UAAU,KAAK,aAAa,IAAI,EAAE;AAExC,QAAI,WAAW,MAAM;AAAE,aAAO;;AAE9B,WAAO,KAAK,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmKvC,OAAQ,OAAc,gBAA+B,CAAA,GAAE;AACrD,UAAM,EAAE,eAAe,oBAAmB,IAAK,KAAK;AACpD,UAAM,4BAAuD,EAAE,GAAG,qBAAqB,GAAG,cAAa;AAEvG,UAAM,aAAa,KAAK,aAAa,OAAO,aAAa;AACzD,UAAM,UAAU,CAAA;AAEhB,eAAW,CAAC,OAAO,EAAE,OAAO,OAAO,MAAK,CAAE,KAAK,YAAY;AAKzD,YAAM,UAAU,MAAM,UAAU;AAEhC,YAAM,SAAS;QACb,IAAI,KAAK,aAAa,IAAI,KAAK;QAC/B,OAAO,QAAQ;QACf,OAAO,OAAO,KAAK,KAAK;QACxB,YAAY;QACZ;;AAGF,aAAO,OAAO,QAAQ,KAAK,cAAc,IAAI,KAAK,CAAC;AACnD,UAAI,0BAA0B,UAAU,QAAQ,0BAA0B,OAAO,MAAM,GAAG;AACxF,gBAAQ,KAAK,MAAM;;;AAMvB,QAAI,UAAU,YAAW,YAAY,0BAA0B,iBAAiB,MAAM;AACpF,aAAO;;AAGT,YAAQ,KAAK,OAAO;AACpB,WAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgET,YAAa,aAAqB,UAAyB,CAAA,GAAE;AAC3D,cAAU,EAAE,GAAG,KAAK,SAAS,oBAAoB,GAAG,QAAO;AAE3D,UAAM,cAA+E,oBAAI,IAAG;AAE5F,eAAW,EAAE,OAAO,MAAK,KAAM,KAAK,OAAO,aAAa,OAAO,GAAG;AAChE,YAAM,SAAS,MAAM,KAAK,GAAG;AAC7B,YAAM,aAAa,YAAY,IAAI,MAAM;AACzC,UAAI,cAAc,MAAM;AACtB,mBAAW,SAAS;AACpB,mBAAW,SAAS;aACf;AACL,oBAAY,IAAI,QAAQ,EAAE,OAAO,OAAO,OAAO,EAAC,CAAE;;;AAItD,UAAM,UAAU,CAAA;AAChB,eAAW,CAAC,YAAY,EAAE,OAAO,OAAO,MAAK,CAAE,KAAK,aAAa;AAC/D,cAAQ,KAAK,EAAE,YAAY,OAAO,OAAO,QAAQ,MAAK,CAAE;;AAG1D,YAAQ,KAAK,OAAO;AACpB,WAAO;;;;;EAMT,IAAI,gBAAa;AACf,WAAO,KAAK;;;;;EAMd,IAAI,YAAS;AACX,WAAO,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;EAwBrB,OAAO,SAAmB,MAAc,SAAmB;AACzD,QAAI,WAAW,MAAM;AACnB,YAAM,IAAI,MAAM,uFAAuF;;AAEzG,WAAO,KAAK,OAAO,KAAK,MAAM,IAAI,GAAG,OAAO;;;;;;;;;;;;;;;EAgB9C,aAAa,cAAwB,MAAc,SAAmB;AACpE,QAAI,WAAW,MAAM;AACnB,YAAM,IAAI,MAAM,uFAAuF;;AAEzG,WAAO,KAAK,YAAY,KAAK,MAAM,IAAI,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;EAwBnD,OAAO,WAAY,YAAkB;AACnC,QAAI,eAAe,eAAe,UAAU,GAAG;AAC7C,aAAO,eAAe,gBAAgB,UAAU;WAC3C;AACL,YAAM,IAAI,MAAM,+BAA+B,UAAU,GAAG;;;;;;EAOhE,OAAO,OAAiB,IAAmB,SAAmB;AAC5D,UAAM,EACJ,OACA,aACA,aACA,cACA,qBAAoB,IAClB;AAEJ,UAAM,aAAa,KAAK,sBAAsB,IAAI,OAAO;AAEzD,eAAW,eAAe,mBAAmB,WAAW;AACxD,eAAW,eAAe,mBAAmB,WAAW;AACxD,eAAW,gBAAgB,mBAAmB,YAAY;AAE1D,eAAW,CAAC,SAAS,EAAE,KAAK,WAAW,cAAc;AACnD,iBAAW,aAAa,IAAI,IAAI,OAAO;;AAGzC,eAAW,CAAC,MAAM,IAAI,KAAK,OAAO;AAChC,YAAM,UAAU,oBAAI,IAAG;AAEvB,iBAAW,WAAW,OAAO,KAAK,IAAI,GAAG;AACvC,YAAI,aAAa,KAAK,OAAO;AAG7B,YAAI,yBAAyB,GAAG;AAC9B,uBAAa,WAAW;;AAG1B,gBAAQ,IAAI,SAAS,SAAS,EAAE,GAAG,mBAAmB,UAAU,CAAsB;;AAGxF,iBAAW,OAAO,IAAI,MAAM,OAAO;;AAGrC,WAAO;;;;;EAMT,aAAa,YAAsB,IAAmB,SAAmB;AACvE,UAAM,EACJ,OACA,aACA,aACA,cACA,qBAAoB,IAClB;AAEJ,UAAM,aAAa,KAAK,sBAAsB,IAAI,OAAO;AAEzD,eAAW,eAAe,MAAM,wBAAwB,WAAW;AACnE,eAAW,eAAe,MAAM,wBAAwB,WAAW;AACnE,eAAW,gBAAgB,MAAM,wBAAwB,YAAY;AAErE,eAAW,CAAC,SAAS,EAAE,KAAK,WAAW,cAAc;AACnD,iBAAW,aAAa,IAAI,IAAI,OAAO;;AAGzC,QAAI,QAAQ;AACZ,eAAW,CAAC,MAAM,IAAI,KAAK,OAAO;AAChC,YAAM,UAAU,oBAAI,IAAG;AAEvB,iBAAW,WAAW,OAAO,KAAK,IAAI,GAAG;AACvC,YAAI,aAAa,KAAK,OAAO;AAG7B,YAAI,yBAAyB,GAAG;AAC9B,uBAAa,WAAW;;AAG1B,gBAAQ,IAAI,SAAS,SAAS,EAAE,GAAG,MAAM,wBAAwB,UAAU,CAAsB;;AAGnG,UAAI,EAAE,QAAQ,QAAS;AAAG,cAAM,KAAK,CAAC;AACtC,iBAAW,OAAO,IAAI,MAAM,OAAO;;AAGrC,WAAO;;;;;EAMD,OAAO,sBAAgC,IAAmB,SAAmB;AACnF,UAAM,EACJ,eACA,QACA,UACA,oBACA,WACA,qBAAoB,IAClB;AAEJ,QAAI,yBAAyB,KAAK,yBAAyB,GAAG;AAC5D,YAAM,IAAI,MAAM,8EAA8E;;AAGhG,UAAM,aAAa,IAAI,YAAW,OAAO;AAEzC,eAAW,iBAAiB;AAC5B,eAAW,UAAU;AACrB,eAAW,eAAe,oBAAI,IAAG;AACjC,eAAW,YAAY;AACvB,eAAW,kBAAkB;AAC7B,eAAW,aAAa,aAAa;AACrC,eAAW,SAAS,IAAI,cAAa;AAErC,WAAO;;;;;EAMD,aAAc,OAAc,gBAA+B,CAAA,GAAE;AACnE,QAAI,UAAU,YAAW,UAAU;AACjC,aAAO,KAAK,qBAAqB,aAAa;;AAGhD,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAMC,WAAU,EAAE,GAAG,eAAe,GAAG,OAAO,SAAS,OAAS;AAChE,YAAMC,WAAU,MAAM,QAAQ,IAAI,CAAC,aAAa,KAAK,aAAa,UAAUD,QAAO,CAAC;AACpF,aAAO,KAAK,eAAeC,UAASD,SAAQ,WAAW;;AAGzD,UAAM,EAAE,UAAU,aAAa,eAAe,oBAAmB,IAAK,KAAK;AAC3E,UAAM,UAAU,EAAE,UAAU,aAAa,GAAG,qBAAqB,GAAG,cAAa;AACjF,UAAM,EAAE,UAAU,gBAAgB,aAAa,kBAAiB,IAAK;AACrE,UAAM,QAAQ,eAAe,KAAK,EAC/B,QAAQ,CAAC,SAAiB,kBAAkB,IAAI,CAAC,EACjD,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI;AAC1B,UAAM,UAAuB,MAAM,IAAI,gBAAgB,OAAO,CAAC;AAC/D,UAAM,UAAU,QAAQ,IAAI,CAAAE,WAAS,KAAK,iBAAiBA,QAAO,OAAO,CAAC;AAE1E,WAAO,KAAK,eAAe,SAAS,QAAQ,WAAW;;;;;EAMjD,iBAAkB,OAAkB,eAA4B;AACtE,UAAM,UAAqC,EAAE,GAAG,KAAK,SAAS,eAAe,GAAG,cAAa;AAE7F,UAAM,UAAU,QAAQ,UAAU,KAAK,SAAS,QAAQ,OAAO,CAACC,SAAQ,WACrE,EAAE,GAAGA,SAAQ,CAAC,KAAK,GAAG,eAAe,QAAQ,OAAO,KAAK,KAAK,EAAC,IAAK,CAAA,CAAE;AAEzE,UAAM,EACJ,eACA,SACA,UACA,MAAM,WAAU,IACd;AAEJ,UAAM,EAAE,OAAO,aAAa,QAAQ,aAAY,IAAK,EAAE,GAAG,qBAAqB,SAAS,GAAG,QAAO;AAElG,UAAM,OAAO,KAAK,OAAO,IAAI,MAAM,IAAI;AACvC,UAAM,UAAU,KAAK,YAAY,MAAM,MAAM,MAAM,MAAM,GAAG,MAAM,WAAW,MAAM,QAAQ,eAAe,UAAU;AAEpH,QAAI;AACJ,QAAI;AAEJ,QAAI,MAAM,QAAQ;AAChB,sBAAgB,KAAK,OAAO,SAAS,MAAM,IAAI;;AAGjD,QAAI,MAAM,OAAO;AACf,YAAM,QAAS,MAAM,UAAU,OAAQ,MAAM,MAAM;AACnD,YAAM,cAAc,QAAQ,IAAI,KAAK,IAAI,UAAU,KAAK,MAAM,MAAM,KAAK,SAAS,KAAK,CAAC,IAAI;AAC5F,UAAI;AAAa,uBAAe,KAAK,OAAO,SAAS,MAAM,MAAM,WAAW;;AAG9E,QAAI,eAAe;AACjB,iBAAW,CAAC,MAAMC,KAAI,KAAK,eAAe;AACxC,cAAM,WAAW,KAAK,SAAS,MAAM,KAAK;AAC1C,YAAI,CAAC,UAAU;AAAE;QAAS;AAI1B,yBAAY,QAAZ,iBAAA,SAAA,SAAA,aAAc,OAAO,IAAI;AAOzB,cAAM,SAAS,eAAe,KAAK,UAAU,KAAK,SAAS,MAAM;AACjE,aAAK,YAAY,MAAM,MAAM,MAAM,QAAQ,MAAM,WAAWA,OAAM,QAAQ,eAAe,YAAY,OAAO;;;AAIhH,QAAI,cAAc;AAChB,iBAAW,QAAQ,aAAa,KAAI,GAAI;AACtC,cAAM,CAACA,OAAM,QAAQ,IAAI,aAAa,IAAI,IAAI;AAC9C,YAAI,CAAC,UAAU;AAAE;QAAS;AAI1B,cAAM,SAAS,cAAc,KAAK,UAAU,KAAK,SAAS;AAC1D,aAAK,YAAY,MAAM,MAAM,MAAM,QAAQ,MAAM,WAAWA,OAAM,QAAQ,eAAe,YAAY,OAAO;;;AAIhH,WAAO;;;;;EAMD,qBAAsB,eAA4B;AACxD,UAAM,UAAU,oBAAI,IAAG;AACvB,UAAM,UAAqC,EAAE,GAAG,KAAK,SAAS,eAAe,GAAG,cAAa;AAE7F,eAAW,CAAC,SAAS,EAAE,KAAK,KAAK,cAAc;AAC7C,YAAM,QAAQ,QAAQ,gBAAgB,QAAQ,cAAc,IAAI,IAAI,KAAK,cAAc,IAAI,OAAO,CAAC,IAAI;AACvG,cAAQ,IAAI,SAAS;QACnB;QACA,OAAO,CAAA;QACP,OAAO,CAAA;MACR,CAAA;;AAGH,WAAO;;;;;EAMD,eAAgB,SAAsB,cAAmC,IAAE;AACjF,QAAI,QAAQ,WAAW,GAAG;AAAE,aAAO,oBAAI,IAAG;;AAC1C,UAAM,WAAW,YAAY,YAAW;AACxC,UAAM,aAAc,YAAmD,QAAQ;AAE/E,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,iCAAiC,WAAW,EAAE;;AAGhE,WAAO,QAAQ,OAAO,UAAU,KAAK,oBAAI,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;EA2B9C,SAAM;AACJ,UAAM,QAA6D,CAAA;AAEnE,eAAW,CAAC,MAAM,UAAU,KAAK,KAAK,QAAQ;AAC5C,YAAM,OAAgD,CAAA;AAEtD,iBAAW,CAAC,SAAS,KAAK,KAAK,YAAY;AACzC,aAAK,OAAO,IAAI,OAAO,YAAY,KAAK;;AAG1C,YAAM,KAAK,CAAC,MAAM,IAAI,CAAC;;AAGzB,WAAO;MACL,eAAe,KAAK;MACpB,QAAQ,KAAK;MACb,aAAa,OAAO,YAAY,KAAK,YAAY;MACjD,UAAU,KAAK;MACf,aAAa,OAAO,YAAY,KAAK,YAAY;MACjD,oBAAoB,KAAK;MACzB,cAAc,OAAO,YAAY,KAAK,aAAa;MACnD,WAAW,KAAK;MAChB;MACA,sBAAsB;;;;;;EAOlB,YACN,YACA,aACA,YACA,WACA,eACA,aACA,iBACA,YACA,UAAqB,oBAAI,IAAG,GAAE;AAE9B,QAAI,iBAAiB;AAAM,aAAO;AAElC,eAAW,SAAS,OAAO,KAAK,WAAW,GAAG;AAC5C,YAAM,aAAa,YAAY,KAAK;AACpC,YAAM,UAAU,KAAK,UAAU,KAAK;AAEpC,YAAM,iBAAiB,cAAc,IAAI,OAAO;AAChD,UAAI,kBAAkB;AAAM;AAE5B,UAAI,iBAAiB,eAAe;AACpC,YAAM,iBAAiB,KAAK,gBAAgB,OAAO;AAEnD,iBAAW,SAAS,eAAe,KAAI,GAAI;AACzC,YAAI,CAAC,KAAK,aAAa,IAAI,KAAK,GAAG;AACjC,eAAK,WAAW,SAAS,OAAO,WAAW;AAC3C,4BAAkB;AAClB;;AAGF,cAAM,WAAW,kBAAkB,gBAAgB,KAAK,aAAa,IAAI,KAAK,GAAG,aAAa,KAAK,cAAc,IAAI,KAAK,CAAC,IAAI;AAC/H,YAAI,CAAC;AAAU;AAEf,cAAM,WAAW,eAAe,IAAI,KAAK;AACzC,cAAM,cAAc,KAAK,aAAa,IAAI,KAAK,EAAG,OAAO;AAQzD,cAAM,WAAW,cAAc,UAAU,gBAAgB,KAAK,gBAAgB,aAAa,gBAAgB,UAAU;AACrH,cAAM,gBAAgB,aAAa,YAAY,aAAa,WAAW;AAEvE,cAAM,SAAS,QAAQ,IAAI,KAAK;AAChC,YAAI,QAAQ;AACV,iBAAO,SAAS;AAChB,2BAAiB,OAAO,OAAO,UAAU;AACzC,gBAAM,QAAQ,eAAe,OAAO,OAAO,WAAW;AACtD,cAAI,OAAO;AACT,kBAAM,KAAK,KAAK;iBACX;AACL,mBAAO,MAAM,WAAW,IAAI,CAAC,KAAK;;eAE/B;AACL,kBAAQ,IAAI,OAAO;YACjB,OAAO;YACP,OAAO,CAAC,UAAU;YAClB,OAAO,EAAE,CAAC,WAAW,GAAG,CAAC,KAAK,EAAC;UAChC,CAAA;;;;AAKP,WAAO;;;;;EAMD,QAAS,SAAiB,YAAoB,MAAY;AAChE,UAAM,YAAY,KAAK,OAAO,MAAM,MAAM,SAAS;AAEnD,QAAI,aAAa,UAAU,IAAI,OAAO;AACtC,QAAI,cAAc,MAAM;AACtB,mBAAa,oBAAI,IAAG;AACpB,iBAAW,IAAI,YAAY,CAAC;AAC5B,gBAAU,IAAI,SAAS,UAAU;WAC5B;AACL,YAAM,OAAO,WAAW,IAAI,UAAU;AACtC,iBAAW,IAAI,aAAa,QAAQ,KAAK,CAAC;;;;;;EAOtC,WAAY,SAAiB,YAAoB,MAAY;AACnE,QAAI,CAAC,KAAK,OAAO,IAAI,IAAI,GAAG;AAC1B,WAAK,oBAAoB,YAAY,SAAS,IAAI;AAClD;;AAGF,UAAM,YAAY,KAAK,OAAO,MAAM,MAAM,SAAS;AAEnD,UAAM,aAAa,UAAU,IAAI,OAAO;AACxC,QAAI,cAAc,QAAQ,WAAW,IAAI,UAAU,KAAK,MAAM;AAC5D,WAAK,oBAAoB,YAAY,SAAS,IAAI;eACzC,WAAW,IAAI,UAAU,KAAM,GAAG;AAC3C,UAAI,WAAW,QAAQ,GAAG;AACxB,kBAAU,OAAO,OAAO;aACnB;AACL,mBAAW,OAAO,UAAU;;WAEzB;AACL,iBAAW,IAAI,YAAY,WAAW,IAAI,UAAU,IAAK,CAAC;;AAG5D,QAAI,KAAK,OAAO,IAAI,IAAI,EAAG,SAAS,GAAG;AACrC,WAAK,OAAO,OAAO,IAAI;;;;;;EAOnB,oBAAqB,iBAAyB,SAAiB,MAAY;AACjF,eAAW,aAAa,OAAO,KAAK,KAAK,SAAS,GAAG;AACnD,UAAI,KAAK,UAAU,SAAS,MAAM,SAAS;AACzC,aAAK,SAAS,OAAO,QAAQ,gCAAgC,KAAK,aAAa,IAAI,eAAe,CAAC,sCAAsC,IAAI,+BAA+B,SAAS,sEAAsE,kBAAkB;AAC7Q;;;;;;;EAQE,cAAe,YAAe;AACpC,UAAM,kBAAkB,KAAK;AAC7B,SAAK,aAAa,IAAI,YAAY,eAAe;AACjD,SAAK,aAAa,IAAI,iBAAiB,UAAU;AACjD,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,WAAO;;;;;EAMD,UAAW,QAAgB;AACjC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,UAAU,OAAO,CAAC,CAAC,IAAI;;;;;;EAOxB,eAAgB,YAAoB,SAAiB,OAAe,QAAc;AACxF,QAAI,eAAe,KAAK,aAAa,IAAI,UAAU;AACnD,QAAI,gBAAgB;AAAM,WAAK,aAAa,IAAI,YAAY,eAAe,CAAA,CAAE;AAC7E,iBAAa,OAAO,IAAI;AAExB,UAAM,qBAAqB,KAAK,gBAAgB,OAAO,KAAK;AAC5D,UAAM,mBAAoB,qBAAqB,QAAS;AACxD,SAAK,gBAAgB,OAAO,IAAI,oBAAoB,QAAQ;;;;;EAMtD,kBAAmB,YAAoB,SAAiB,OAAe,QAAc;AAC3F,QAAI,UAAU,GAAG;AACf,WAAK,gBAAgB,OAAO,IAAI;AAChC;;AAEF,UAAM,mBAAoB,KAAK,gBAAgB,OAAO,IAAI,QAAS;AACnE,SAAK,gBAAgB,OAAO,IAAI,oBAAoB,QAAQ;;;;;EAMtD,iBAAkB,YAAoB,KAAM;AAClD,UAAM,EAAE,aAAa,aAAY,IAAK,KAAK;AAC3C,QAAI,eAAe,QAAQ,YAAY,WAAW,GAAG;AAAE;;AAEvD,QAAI,iBAAiB,KAAK,cAAc,IAAI,UAAU;AACtD,QAAI,kBAAkB;AAAM,WAAK,cAAc,IAAI,YAAY,iBAAiB,CAAA,CAAE;AAElF,eAAW,aAAa,aAAa;AACnC,YAAM,aAAa,aAAa,KAAK,SAAS;AAC9C,UAAI,eAAe;AAAW,uBAAe,SAAS,IAAI;;;;AAl5C9C,WAAA,WAA0B,OAAO,GAAG;AAu5CtD,IAAM,iBAAiB,CAAC,QAAa,aACnC,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ,IAAI,OAAO,QAAQ,IAAI;AAI9E,IAAM,cAAwE;EAC5E,CAAC,EAAE,GAAG,CAAC,GAAc,MAAgB;AACnC,eAAW,SAAS,EAAE,KAAI,GAAI;AAC5B,YAAM,WAAW,EAAE,IAAI,KAAK;AAC5B,UAAI,YAAY,MAAM;AACpB,UAAE,IAAI,OAAO,EAAE,IAAI,KAAK,CAAE;aACrB;AACL,cAAM,EAAE,OAAO,OAAO,MAAK,IAAK,EAAE,IAAI,KAAK;AAC3C,iBAAS,QAAQ,SAAS,QAAQ;AAClC,iBAAS,QAAQ,OAAO,OAAO,SAAS,OAAO,KAAK;AACpD,0BAAkB,SAAS,OAAO,KAAK;;;AAI3C,WAAO;;EAET,CAAC,GAAG,GAAG,CAAC,GAAc,MAAgB;AACpC,UAAM,WAAW,oBAAI,IAAG;AAExB,eAAW,SAAS,EAAE,KAAI,GAAI;AAC5B,YAAM,WAAW,EAAE,IAAI,KAAK;AAC5B,UAAI,YAAY;AAAM;AAEtB,YAAM,EAAE,OAAO,OAAO,MAAK,IAAK,EAAE,IAAI,KAAK;AAC3C,wBAAkB,SAAS,OAAO,KAAK;AACvC,eAAS,IAAI,OAAO;QAClB,OAAO,SAAS,QAAQ;QACxB,OAAO,SAAS;QAChB,OAAO,OAAO,OAAO,SAAS,OAAO,KAAK;MAC3C,CAAA;;AAGH,WAAO;;EAET,CAAC,OAAO,GAAG,CAAC,GAAc,MAAgB;AACxC,eAAW,SAAS,EAAE,KAAI;AAAI,QAAE,OAAO,KAAK;AAC5C,WAAO;;;AA6CX,IAAM,oBAAgC,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAG;AAE9D,IAAM,gBAAgB,CACpB,UACA,eACA,YACA,aACA,gBACA,eACU;AACV,QAAM,EAAE,GAAG,GAAG,EAAC,IAAK;AACpB,QAAM,aAAa,KAAK,IAAI,KAAK,aAAa,gBAAgB,QAAQ,gBAAgB,IAAI;AAC1F,SAAO,cAAc,IAAI,YAAY,IAAI,MAAM,WAAW,KAAK,IAAI,IAAI,IAAI,cAAc;AAC3F;AAEA,IAAM,kBAAkB,CAAC,YAA2B,CAAC,MAAc,GAAW,UAA8B;AAC1G,QAAM,QAAS,OAAO,QAAQ,UAAU,aACpC,QAAQ,MAAM,MAAM,GAAG,KAAK,IAC3B,QAAQ,SAAS;AACtB,QAAM,SAAU,OAAO,QAAQ,WAAW,aACtC,QAAQ,OAAO,MAAM,GAAG,KAAK,IAC5B,QAAQ,WAAW;AACxB,QAAM,YAAa,OAAO,QAAQ,cAAc,aAC5C,QAAQ,UAAU,MAAM,GAAG,KAAK,IAChC;AACJ,SAAO,EAAE,MAAM,OAAO,QAAQ,UAAS;AACzC;AAEA,IAAM,iBAAiB;EACrB,SAAS;EACT,cAAc,CAAC,UAAe,cAAsB,SAAS,SAAS;EACtE,UAAU,CAAC,SAAiB,KAAK,MAAM,oBAAoB;EAC3D,aAAa,CAAC,SAAiB,KAAK,YAAW;EAC/C,QAAQ;EACR,eAAe;EACf,aAAa,CAAA;EACb,QAAQ,CAAC,OAAiB,YAAyB;AACjD,QAAI,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAU,KAAK,OAAM;AAAY,cAAQ,KAAK,EAAE,OAAO;;EAEpE,YAAY;;AAGd,IAAM,uBAAuB;EAC3B,aAAa;EACb,QAAQ;EACR,OAAO;EACP,UAAU;EACV,OAAO,CAAA;EACP,SAAS,EAAE,OAAO,MAAM,QAAQ,MAAK;EACrC,MAAM;;AAGR,IAAM,4BAA4B;EAChC,aAAa;EACb,QAAQ,CAAC,MAAc,GAAW,UAChC,MAAM,MAAM,SAAS;;AAGzB,IAAM,uBAAuB,EAAE,WAAW,KAAM,WAAW,GAAE;AAC7D,IAAM,0BAA0B,EAAE,eAAe,KAAK,cAAc,GAAE;AAEtE,IAAM,2BAA2B,EAAE,GAAG,sBAAsB,GAAG,wBAAuB;AAEtF,IAAM,mBAAmB,CAAC,QAAkB,SAAsB;AAEhE,MAAI,CAAC,OAAO,SAAS,IAAI;AAAG,WAAO,KAAK,IAAI;AAC9C;AAEA,IAAM,oBAAoB,CAAC,QAAkB,WAAmC;AAC9E,aAAW,QAAQ,QAAQ;AAEzB,QAAI,CAAC,OAAO,SAAS,IAAI;AAAG,aAAO,KAAK,IAAI;;AAEhD;AAGA,IAAM,UAAU,CAAC,EAAE,OAAO,EAAC,GAAY,EAAE,OAAO,EAAC,MAAe,IAAI;AAEpE,IAAM,YAAY,MAAM,oBAAI,IAAG;AAM/B,IAAM,qBAAqB,CAAI,WAAgD;AAC7E,QAAM,MAAM,oBAAI,IAAG;AAEnB,aAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,QAAI,IAAI,SAAS,KAAK,EAAE,GAAG,OAAO,GAAG,CAAC;;AAGxC,SAAO;AACT;AAEA,IAAM,0BAA0B,OAAU,WAAyD;AACjG,QAAM,MAAM,oBAAI,IAAG;AAEnB,MAAI,QAAQ;AACZ,aAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,QAAI,IAAI,SAAS,KAAK,EAAE,GAAG,OAAO,GAAG,CAAC;AACtC,QAAI,EAAE,QAAQ,QAAS,GAAG;AACxB,YAAM,KAAK,CAAC;;;AAIhB,SAAO;AACT;AAEA,IAAM,OAAO,CAAC,OAAe,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAI7E,IAAM,uBAAuB;", "names": ["last", "node", "child", "key", "chunk", "promise", "conditions", "options", "results", "query", "boosts", "data"]}